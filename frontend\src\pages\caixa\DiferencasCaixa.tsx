import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { cashDifferenceService, CashDifferenceAlert, ResolveAlertRequest } from '@/services/cashDifferenceService';
import { AlertTriangle, RefreshCw, Filter, Eye, CheckCircle, X, TrendingUp, TrendingDown } from 'lucide-react';

const DiferencasCaixa: React.FC = () => {
  const { toast } = useToast();
  
  // Estados principais
  const [alerts, setAlerts] = useState<CashDifferenceAlert[]>([]);
  const [pagination, setPagination] = useState<any>({ page: 1, limit: 20, total: 0, totalPages: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);

  // Estados do modal
  const [selectedAlert, setSelectedAlert] = useState<CashDifferenceAlert | null>(null);
  const [isResolveModalOpen, setIsResolveModalOpen] = useState(false);
  const [resolveAction, setResolveAction] = useState<'resolved' | 'dismissed'>('resolved');
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [isResolving, setIsResolving] = useState(false);

  // Filtros
  const [filters, setFilters] = useState({
    status: 'all',
    start_date: '',
    end_date: ''
  });

  // Carregar alertas
  const loadAlerts = async (page: number = 1) => {
    setIsLoading(true);
    try {
      const params: any = { page, limit: pagination.limit };

      if (filters.status && filters.status !== 'all') params.status = filters.status;
      if (filters.start_date) params.start_date = filters.start_date;
      if (filters.end_date) params.end_date = filters.end_date;

      const data = await cashDifferenceService.getAlerts(params);
      setAlerts(data.alerts);
      setPagination(data.pagination);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar alertas',
        description: error.message || 'Não foi possível carregar os alertas de diferença'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar estatísticas
  const loadStats = async () => {
    setIsLoadingStats(true);
    try {
      const data = await cashDifferenceService.getStats();
      setStats(data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar estatísticas',
        description: error.message || 'Não foi possível carregar as estatísticas'
      });
    } finally {
      setIsLoadingStats(false);
    }
  };

  // Resolver alerta
  const handleResolveAlert = async () => {
    if (!selectedAlert) return;

    setIsResolving(true);
    try {
      const data: ResolveAlertRequest = {
        status: resolveAction,
        resolution_notes: resolutionNotes.trim() || undefined
      };

      await cashDifferenceService.resolveAlert(selectedAlert.id, data);

      toast({
        title: 'Alerta Processado',
        description: `Alerta ${resolveAction === 'resolved' ? 'resolvido' : 'descartado'} com sucesso`,
      });

      // Recarregar dados
      await Promise.all([loadAlerts(pagination.page), loadStats()]);

      // Fechar modal
      setIsResolveModalOpen(false);
      setSelectedAlert(null);
      setResolutionNotes('');
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao processar alerta',
        description: error.message || 'Não foi possível processar o alerta'
      });
    } finally {
      setIsResolving(false);
    }
  };

  // Aplicar filtros
  const handleApplyFilters = () => {
    loadAlerts(1);
  };

  // Limpar filtros
  const handleClearFilters = () => {
    setFilters({ status: 'all', start_date: '', end_date: '' });
    setTimeout(() => loadAlerts(1), 100);
  };

  // Atualizar dados
  const handleRefresh = () => {
    loadAlerts(pagination.page);
    loadStats();
  };

  useEffect(() => {
    loadAlerts();
    loadStats();
  }, []);

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Diferenças de Caixa</h1>
          <p className="text-muted-foreground">Gerencie alertas de diferenças detectadas nas entregas</p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      {/* Cards de Estatísticas */}
      {!isLoadingStats && stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total de Alertas</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.overview.total_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pendentes</CardTitle>
              <TrendingUp className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.overview.pending_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Resolvidos</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.overview.resolved_alerts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {cashDifferenceService.formatCurrency(stats.overview.total_difference_amount)}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters({ ...filters, status: value })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="pending">Pendentes</SelectItem>
                  <SelectItem value="resolved">Resolvidos</SelectItem>
                  <SelectItem value="dismissed">Descartados</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="start_date">Data Inicial</Label>
              <Input
                id="start_date"
                type="date"
                value={filters.start_date}
                onChange={(e) => setFilters({ ...filters, start_date: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">Data Final</Label>
              <Input
                id="end_date"
                type="date"
                value={filters.end_date}
                onChange={(e) => setFilters({ ...filters, end_date: e.target.value })}
              />
            </div>

            <div className="space-y-2 flex items-end gap-2">
              <Button onClick={handleApplyFilters} className="flex-1">
                Aplicar
              </Button>
              <Button onClick={handleClearFilters} variant="outline">
                Limpar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Alertas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5" />
            Alertas de Diferença
          </CardTitle>
          <CardDescription>
            Total de {pagination.total} alerta(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">Carregando alertas...</div>
          ) : alerts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="mx-auto h-12 w-12 mb-4 text-green-500" />
              <p>Nenhum alerta de diferença encontrado</p>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Operador</TableHead>
                      <TableHead>Origem</TableHead>
                      <TableHead className="text-right">Declarado</TableHead>
                      <TableHead className="text-right">Confirmado</TableHead>
                      <TableHead className="text-right">Diferença</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {alerts.map((alert) => (
                      <TableRow key={alert.id}>
                        <TableCell className="font-medium">
                          {cashDifferenceService.formatDate(alert.created_at)}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{alert.operator.name}</div>
                            <div className="text-sm text-muted-foreground">{alert.operator.role}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{alert.source.name}</div>
                            {alert.source.branch && (
                              <div className="text-sm text-muted-foreground">{alert.source.branch.name}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {cashDifferenceService.formatCurrency(alert.declared_amount)}
                        </TableCell>
                        <TableCell className="text-right">
                          {cashDifferenceService.formatCurrency(alert.confirmed_amount)}
                        </TableCell>
                        <TableCell className={`text-right font-semibold ${
                          alert.difference_amount > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {alert.difference_amount > 0 ? '+' : ''}
                          {cashDifferenceService.formatCurrency(alert.difference_amount)}
                        </TableCell>
                        <TableCell>
                          <Badge className={cashDifferenceService.getStatusColor(alert.status)}>
                            {cashDifferenceService.getStatusIcon(alert.status)} {cashDifferenceService.getStatusText(alert.status)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {alert.status === 'pending' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedAlert(alert);
                                setResolveAction('resolved');
                                setResolutionNotes('');
                                setIsResolveModalOpen(true);
                              }}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Processar
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Paginação */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Página {pagination.page} de {pagination.totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadAlerts(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      Anterior
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadAlerts(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Próxima
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Modal de Resolução */}
      <Dialog open={isResolveModalOpen} onOpenChange={(open) => {
        setIsResolveModalOpen(open);
        if (!open) {
          setSelectedAlert(null);
          setResolutionNotes('');
        }
      }}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Processar Alerta de Diferença</DialogTitle>
            <DialogDescription>
              Resolva ou descarte este alerta de diferença de caixa
            </DialogDescription>
          </DialogHeader>

          {selectedAlert && (
            <div className="space-y-4">
              {/* Detalhes do Alerta */}
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Operador:</span>
                  <span>{selectedAlert.operator.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Origem:</span>
                  <span>{selectedAlert.source.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Valor Declarado:</span>
                  <span>{cashDifferenceService.formatCurrency(selectedAlert.declared_amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Valor Confirmado:</span>
                  <span>{cashDifferenceService.formatCurrency(selectedAlert.confirmed_amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Diferença:</span>
                  <span className={`font-bold ${
                    selectedAlert.difference_amount > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {selectedAlert.difference_amount > 0 ? '+' : ''}
                    {cashDifferenceService.formatCurrency(selectedAlert.difference_amount)}
                  </span>
                </div>
              </div>

              {/* Ação */}
              <div className="space-y-2">
                <Label htmlFor="resolve_action">Ação</Label>
                <Select
                  value={resolveAction}
                  onValueChange={(value: 'resolved' | 'dismissed') => setResolveAction(value)}
                >
                  <SelectTrigger id="resolve_action">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="resolved">Marcar como Resolvido</SelectItem>
                    <SelectItem value="dismissed">Descartar Alerta</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="resolution_notes">Observações</Label>
                <Textarea
                  id="resolution_notes"
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  placeholder="Adicione observações sobre a resolução..."
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsResolveModalOpen(false)}
              disabled={isResolving}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleResolveAlert}
              disabled={isResolving}
            >
              {isResolving ? 'Processando...' : (resolveAction === 'resolved' ? 'Resolver' : 'Descartar')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DiferencasCaixa;
