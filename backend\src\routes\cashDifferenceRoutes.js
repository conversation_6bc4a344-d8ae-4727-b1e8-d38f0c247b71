const express = require('express');
const Joi = require('joi');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authenticate, authorize } = require('../auth/middleware');
const logger = require('../core/logger');

const router = express.Router();

// Aplicar autenticação a todas as rotas
router.use(authenticate);

// Schema de validação para resolver alerta
const resolveAlertSchema = Joi.object({
  status: Joi.string().valid('resolved', 'dismissed').required(),
  resolution_notes: Joi.string().max(1000).optional().allow('')
});

/**
 * GET /api/cash-differences
 * Listar alertas de diferença de caixa (Gerente e Admin)
 */
router.get('/', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    status = 'all',
    start_date = null,
    end_date = null
  } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);
  let whereConditions = ['1=1'];
  let params = [];

  // Filtro por status
  if (status && status !== 'all') {
    whereConditions.push('cda.status = ?');
    params.push(status);
  }

  // Filtro por data
  if (start_date) {
    whereConditions.push('DATE(cda.created_at) >= ?');
    params.push(start_date);
  }

  if (end_date) {
    whereConditions.push('DATE(cda.created_at) <= ?');
    params.push(end_date);
  }

  const whereClause = whereConditions.join(' AND ');

  // Consulta principal
  const alerts = await executeQuery(
    `SELECT 
      cda.id,
      cda.delivery_id,
      cda.declared_amount,
      cda.confirmed_amount,
      cda.difference_amount,
      cda.status,
      cda.resolution_notes,
      cda.created_at,
      cda.resolved_at,
      
      -- Dados do operador
      op.full_name as operator_name,
      op.email as operator_email,
      op_role.name as operator_role,
      
      -- Dados do tesoureiro
      tr.full_name as treasurer_name,
      tr.email as treasurer_email,
      
      -- Dados do resolvedor (se resolvido)
      res.full_name as resolved_by_name,
      res.email as resolved_by_email,
      
      -- Dados do caixa/origem
      cr.register_number,
      cr.description as cash_register_description,
      b.name as branch_name,
      b.code as branch_code
      
    FROM cash_difference_alerts cda
    JOIN users op ON cda.operator_id = op.id
    JOIN roles op_role ON op.role_id = op_role.id
    JOIN users tr ON cda.treasurer_id = tr.id
    LEFT JOIN users res ON cda.resolved_by = res.id
    LEFT JOIN cash_registers cr ON cda.cash_register_id = cr.id
    LEFT JOIN branches b ON cr.branch_id = b.id
    WHERE ${whereClause}
    ORDER BY cda.created_at DESC
    LIMIT ? OFFSET ?`,
    [...params, parseInt(limit), offset]
  );

  // Contar total
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total
     FROM cash_difference_alerts cda
     WHERE ${whereClause}`,
    params
  );

  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / parseInt(limit));

  // Formatar dados
  const formattedAlerts = alerts.map(alert => ({
    id: alert.id,
    delivery_id: alert.delivery_id,
    declared_amount: parseFloat(alert.declared_amount),
    confirmed_amount: parseFloat(alert.confirmed_amount),
    difference_amount: parseFloat(alert.difference_amount),
    status: alert.status,
    resolution_notes: alert.resolution_notes,
    created_at: alert.created_at,
    resolved_at: alert.resolved_at,
    operator: {
      name: alert.operator_name,
      email: alert.operator_email,
      role: alert.operator_role
    },
    treasurer: {
      name: alert.treasurer_name,
      email: alert.treasurer_email
    },
    resolved_by: alert.resolved_by_name ? {
      name: alert.resolved_by_name,
      email: alert.resolved_by_email
    } : null,
    source: {
      type: alert.cash_register_id ? 'cash_register' : 'vault',
      name: alert.cash_register_id ? `Caixa ${alert.register_number}` : 'Cofre Principal',
      description: alert.cash_register_description,
      branch: alert.branch_name ? {
        name: alert.branch_name,
        code: alert.branch_code
      } : null
    }
  }));

  res.status(200).json({
    status: 'success',
    data: {
      alerts: formattedAlerts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      }
    }
  });
}));

/**
 * GET /api/cash-differences/stats
 * Estatísticas de alertas de diferença (Gerente e Admin)
 */
router.get('/stats', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const [overviewStats, monthlyStats, operatorStats] = await Promise.all([
    // Estatísticas gerais
    executeQuery(`
      SELECT 
        COUNT(*) as total_alerts,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_alerts,
        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_alerts,
        COUNT(CASE WHEN status = 'dismissed' THEN 1 END) as dismissed_alerts,
        SUM(ABS(difference_amount)) as total_difference_amount,
        AVG(ABS(difference_amount)) as avg_difference_amount
      FROM cash_difference_alerts
    `),
    
    // Alertas por mês (últimos 6 meses)
    executeQuery(`
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as count,
        SUM(ABS(difference_amount)) as total_amount
      FROM cash_difference_alerts
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month DESC
    `),
    
    // Alertas por operador (top 10)
    executeQuery(`
      SELECT 
        u.full_name as operator_name,
        u.email as operator_email,
        r.name as role_name,
        COUNT(*) as alert_count,
        SUM(ABS(cda.difference_amount)) as total_difference
      FROM cash_difference_alerts cda
      JOIN users u ON cda.operator_id = u.id
      JOIN roles r ON u.role_id = r.id
      GROUP BY u.id, u.full_name, u.email, r.name
      ORDER BY alert_count DESC
      LIMIT 10
    `)
  ]);

  res.status(200).json({
    status: 'success',
    data: {
      overview: {
        ...overviewStats[0],
        total_difference_amount: parseFloat(overviewStats[0].total_difference_amount || 0),
        avg_difference_amount: parseFloat(overviewStats[0].avg_difference_amount || 0)
      },
      monthly_trends: monthlyStats.map(stat => ({
        ...stat,
        total_amount: parseFloat(stat.total_amount)
      })),
      top_operators: operatorStats.map(stat => ({
        ...stat,
        total_difference: parseFloat(stat.total_difference)
      }))
    }
  });
}));

/**
 * PUT /api/cash-differences/:id/resolve
 * Resolver ou descartar alerta de diferença (Gerente e Admin)
 */
router.put('/:id/resolve', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const alertId = req.params.id;
  const { error, value } = resolveAlertSchema.validate(req.body);
  
  if (error) {
    return res.status(400).json({
      status: 'error',
      message: error.details[0].message
    });
  }

  const { status, resolution_notes } = value;
  const resolvedBy = req.user.id;

  // Verificar se o alerta existe e está pendente
  const alerts = await executeQuery(
    'SELECT id, status FROM cash_difference_alerts WHERE id = ?',
    [alertId]
  );

  if (!alerts.length) {
    return res.status(404).json({
      status: 'error',
      message: 'Alerta não encontrado'
    });
  }

  if (alerts[0].status !== 'pending') {
    return res.status(400).json({
      status: 'error',
      message: 'Alerta já foi processado'
    });
  }

  // Atualizar status do alerta
  await executeQuery(
    `UPDATE cash_difference_alerts 
     SET status = ?, resolution_notes = ?, resolved_by = ?, resolved_at = NOW()
     WHERE id = ?`,
    [status, resolution_notes || null, resolvedBy, alertId]
  );

  logger.info(`Alerta de diferença ${alertId} ${status} por ${req.user.email}`, {
    alertId,
    status,
    resolvedBy,
    resolution_notes
  });

  res.status(200).json({
    status: 'success',
    message: `Alerta ${status === 'resolved' ? 'resolvido' : 'descartado'} com sucesso`,
    data: {
      alert_id: alertId,
      new_status: status,
      resolved_by: req.user.full_name,
      resolved_at: new Date().toISOString()
    }
  });
}));

module.exports = router;
