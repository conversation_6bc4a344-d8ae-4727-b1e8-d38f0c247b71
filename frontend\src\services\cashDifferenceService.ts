import { makeRequest } from '@/utils/api';

// Interfaces para alertas de diferença de caixa
export interface CashDifferenceAlert {
  id: string;
  delivery_id: string;
  declared_amount: number;
  confirmed_amount: number;
  difference_amount: number;
  status: 'pending' | 'resolved' | 'dismissed';
  resolution_notes?: string;
  created_at: string;
  resolved_at?: string;
  operator: {
    name: string;
    email: string;
    role: string;
  };
  treasurer: {
    name: string;
    email: string;
  };
  resolved_by?: {
    name: string;
    email: string;
  };
  source: {
    type: 'cash_register' | 'vault';
    name: string;
    description?: string;
    branch?: {
      name: string;
      code: string;
    };
  };
}

export interface CashDifferenceStats {
  overview: {
    total_alerts: number;
    pending_alerts: number;
    resolved_alerts: number;
    dismissed_alerts: number;
    total_difference_amount: number;
    avg_difference_amount: number;
  };
  monthly_trends: Array<{
    month: string;
    count: number;
    total_amount: number;
  }>;
  top_operators: Array<{
    operator_name: string;
    operator_email: string;
    role_name: string;
    alert_count: number;
    total_difference: number;
  }>;
}

export interface ResolveAlertRequest {
  status: 'resolved' | 'dismissed';
  resolution_notes?: string;
}

class CashDifferenceService {
  // Listar alertas de diferença
  async getAlerts(params?: {
    page?: number;
    limit?: number;
    status?: 'all' | 'pending' | 'resolved' | 'dismissed';
    start_date?: string;
    end_date?: string;
  }): Promise<{
    alerts: CashDifferenceAlert[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.status && params.status !== 'all') queryParams.append('status', params.status);
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);

    const endpoint = `/cash-differences${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await makeRequest<any>(endpoint);
    return response.data;
  }

  // Obter estatísticas de alertas
  async getStats(): Promise<CashDifferenceStats> {
    const response = await makeRequest<{ data: CashDifferenceStats }>('/cash-differences/stats');
    return response.data;
  }

  // Resolver ou descartar alerta
  async resolveAlert(alertId: string, data: ResolveAlertRequest): Promise<any> {
    const response = await makeRequest<any>(`/cash-differences/${alertId}/resolve`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
    return response.data;
  }

  // Formatar valor monetário
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }

  // Formatar data
  formatDate(dateString: string): string {
    return new Intl.DateTimeFormat('pt-AO', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  }

  // Obter cor do status
  getStatusColor(status: string): string {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      case 'dismissed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Obter texto do status
  getStatusText(status: string): string {
    switch (status) {
      case 'pending':
        return 'Pendente';
      case 'resolved':
        return 'Resolvido';
      case 'dismissed':
        return 'Descartado';
      default:
        return status;
    }
  }

  // Obter ícone do status
  getStatusIcon(status: string): string {
    switch (status) {
      case 'pending':
        return '⏳';
      case 'resolved':
        return '✅';
      case 'dismissed':
        return '❌';
      default:
        return '❓';
    }
  }
}

export const cashDifferenceService = new CashDifferenceService();
