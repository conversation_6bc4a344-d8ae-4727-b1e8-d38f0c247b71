[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:Teste E2E - Passo 4: Simulação de Diferença (Sobra) DESCRIPTION:Testar cenário de sobra com 21.000 Kz vs 20.000 Kz esperados e validar notificações. PARCIAL: Detecção funciona, mas confirmação falha por bug SQL. Notificações não aparecem.
-[/] NAME:confirmar que apos o teste as notificacoes aparecem no header DESCRIPTION:Confirmar que após o teste as notificações aparecem no header. PROBLEMA: Nenhuma notificação aparece. Página 'Diferenças de Caixa' mostra 0 alertas. Investigação necessária.
-[ ] NAME:Corrigir Bug Crítico de SQL - 'treasurer_id' DESCRIPTION:PRIORIDADE MÁXIMA: Investigar e corrigir erro 'Unknown column treasurer_id in WHERE' no endpoint POST /api/treasury/confirm-delivery. Localizar coluna correta na tabela treasury_deliveries e atualizar queries.
-[/] NAME:Investigar Base de Dados de Notificações DESCRIPTION:Conectar à base de dados via twins_db e verificar tabelas: notifications, notification_types, cash_difference_alerts. Investigar por que notificações não aparecem no header.
-[ ] NAME:Validar Fluxo Completo de Diferenças DESCRIPTION:Após correções, testar novamente Passo 4.4 e 4.5: confirmar entrega com diferença, verificar alertas na página 'Diferenças de Caixa' e notificações no header.
-[ ] NAME:Correção de Bugs de UI - Modo Dark DESCRIPTION:Corrigir problemas de visibilidade de texto no modo dark nos cards de entregas pendentes e modal de confirmação