-- Seed dos tipos de notificação
INSERT INTO `notification_types` (`code`, `name`, `description`, `target_roles`) VALUES
-- Notificações de Tesouraria
('TREASURY_DELIVERY_PENDING', 'Entrega Pendente para Tesoureiro', 'Nova entrega aguarda confirmação do tesoureiro', '["tesoureiro"]'),
('TREASURY_DELIVERY_CONFIRMED', 'Entrega Confirmada', 'Entrega foi confirmada pelo tesoureiro', '["caixa", "balcao"]'),
('TREASURY_SUPPLY_RECEIVED', 'Suprimento Recebido', 'Recebeu suprimento da tesouraria', '["caixa", "balcao"]'),

-- <PERSON>ertas de Diferença
('CASH_DIFFERENCE_ALERT', 'Alerta de Diferença de Caixa', 'Diferença detectada entre valor declarado e confirmado', '["gerente", "admin"]'),

-- Aprovações de Conta
('ACCOUNT_APPROVAL_PENDING', 'Conta Aguarda Aprovação', 'Nova conta de cliente aguarda aprovação', '["gerente", "admin"]'),
('ACCOUNT_APPROVED', 'Conta Aprovada', 'Conta de cliente foi aprovada', '["caixa", "balcao"]'),
('ACCOUNT_REJECTED', 'Conta Rejeitada', 'Conta de cliente foi rejeitada', '["caixa", "balcao"]'),

-- Atualizações de Sistema
('EXCHANGE_RATE_UPDATED', 'Taxas de Câmbio Atualizadas', 'As taxas de câmbio foram atualizadas', '["tesoureiro", "caixa", "balcao", "gerente", "admin"]'),

-- Tipos Futuros (preparados para implementação posterior)
('CARD_APPROVAL_PENDING', 'Cartão Aguarda Aprovação', 'Novo cartão aguarda aprovação', '["gerente", "admin"]'),
('CARD_APPROVED', 'Cartão Aprovado', 'Cartão foi aprovado', '["caixa", "balcao"]'),
('CARD_REJECTED', 'Cartão Rejeitado', 'Cartão foi rejeitado', '["caixa", "balcao"]'),
('NEW_TASK_ASSIGNED', 'Nova Tarefa Atribuída', 'Nova tarefa foi atribuída', '["admin", "gerente", "tesoureiro", "caixa", "balcao", "tecnico"]'),
('TASK_STATUS_UPDATED', 'Status de Tarefa Atualizado', 'Status de uma tarefa foi atualizado', '["admin", "gerente", "tesoureiro", "caixa", "balcao", "tecnico"]');
