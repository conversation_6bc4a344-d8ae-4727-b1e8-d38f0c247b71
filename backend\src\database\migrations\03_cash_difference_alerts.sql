-- Tabela de alertas de diferença de caixa
CREATE TABLE IF NOT EXISTS `cash_difference_alerts` (
    `id` CHAR(36) PRIMARY KEY,
    `delivery_id` CHAR(36) NOT NULL,
    `cash_register_id` INT NULL,
    `operator_id` CHAR(36) NOT NULL COMMENT 'ID do operador que fez a entrega',
    `treasurer_id` CHAR(36) NOT NULL COMMENT 'ID do tesoureiro que confirmou',
    `declared_amount` DECIMAL(15,2) NOT NULL,
    `confirmed_amount` DECIMAL(15,2) NOT NULL,
    `difference_amount` DECIMAL(15,2) NOT NULL,
    `status` ENUM('pending', 'resolved', 'dismissed') DEFAULT 'pending',
    `resolution_notes` TEXT NULL,
    `resolved_by` CHAR(36) NULL,
    `resolved_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OREIGN KEY (`delivery_id`) REFERENCES `treasury_deliveries`(`id`),
    FOREIGN KEY (`cash_register_id`) REFERENCES `cash_registers`(`id`),
    FOREIGN KEY (`operator_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`treasurer_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`),
    INDEX `idx_cash_difference_delivery` (`delivery_id`),
    INDEX `idx_cash_difference_status` (`status`),
    INDEX `idx_cash_difference_operator` (`operator_id`),
    INDEX `idx_cash_difference_treasurer` (`treasurer_id`),
    INDEX `idx_cash_difference_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
