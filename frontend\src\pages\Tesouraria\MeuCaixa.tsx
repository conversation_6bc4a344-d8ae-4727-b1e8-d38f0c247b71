import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { treasuryService, PendingDelivery, ConfirmDeliveryRequest } from '@/services/treasuryService';
import { Wallet, TrendingUp, TrendingDown, RefreshCw, Calendar, Filter, Clock, Eye, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Componente principal da página "Meu Caixa"
const MeuCaixa: React.FC = () => {
  const { toast } = useToast();
  const [balance, setBalance] = useState<any>(null);
  const [movements, setMovements] = useState<any[]>([]);
  const [pagination, setPagination] = useState<any>({ page: 1, limit: 20, total: 0, totalPages: 0 });
  const [isLoadingBalance, setIsLoadingBalance] = useState(true);
  const [isLoadingMovements, setIsLoadingMovements] = useState(true);

  // Estados para entregas pendentes
  const [pendingDeliveries, setPendingDeliveries] = useState<PendingDelivery[]>([]);
  const [isLoadingDeliveries, setIsLoadingDeliveries] = useState(true);
  const [selectedDelivery, setSelectedDelivery] = useState<PendingDelivery | null>(null);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [isConfirming, setIsConfirming] = useState(false);

  // Estados do modal de confirmação
  const [confirmedAmount, setConfirmedAmount] = useState('');
  const [observations, setObservations] = useState('');
  
  // Filtros
  const [filters, setFilters] = useState({
    movement_type: 'all',
    start_date: '',
    end_date: ''
  });

  // Carregar saldo do tesoureiro
  const loadBalance = async () => {
    setIsLoadingBalance(true);
    try {
      const data = await treasuryService.getMyBalance();
      setBalance(data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar saldo',
        description: error.message || 'Não foi possível carregar o saldo do tesoureiro'
      });
    } finally {
      setIsLoadingBalance(false);
    }
  };

  // Carregar movimentações
  const loadMovements = async (page: number = 1) => {
    setIsLoadingMovements(true);
    try {
      const params: any = { page, limit: pagination.limit };

      if (filters.movement_type && filters.movement_type !== 'all') params.movement_type = filters.movement_type;
      if (filters.start_date) params.start_date = filters.start_date;
      if (filters.end_date) params.end_date = filters.end_date;

      const data = await treasuryService.getMyMovements(params);
      setMovements(data.movements);
      setPagination(data.pagination);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar movimentações',
        description: error.message || 'Não foi possível carregar o histórico de movimentações'
      });
    } finally {
      setIsLoadingMovements(false);
    }
  };

  // Carregar entregas pendentes
  const loadPendingDeliveries = async () => {
    setIsLoadingDeliveries(true);
    try {
      const data = await treasuryService.getPendingDeliveries({ limit: 10 });
      setPendingDeliveries(data.deliveries || []);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao carregar entregas pendentes',
        description: error.message || 'Não foi possível carregar as entregas pendentes'
      });
    } finally {
      setIsLoadingDeliveries(false);
    }
  };

  // Aplicar filtros
  const handleApplyFilters = () => {
    loadMovements(1);
  };

  // Limpar filtros
  const handleClearFilters = () => {
    setFilters({ movement_type: 'all', start_date: '', end_date: '' });
    setTimeout(() => loadMovements(1), 100);
  };

  // Confirmar entrega
  const handleConfirmDelivery = async (data: ConfirmDeliveryRequest) => {
    if (!selectedDelivery) return;

    setIsConfirming(true);
    try {
      const result = await treasuryService.confirmDelivery(selectedDelivery.id, data);

      toast({
        title: 'Entrega Confirmada',
        description: `Entrega de ${formatCurrency(result.confirmed_amount)} confirmada com sucesso${result.has_difference ? ' (diferença detectada)' : ''}`,
      });

      // Recarregar dados
      await Promise.all([
        loadBalance(),
        loadPendingDeliveries(),
        loadMovements(pagination.page)
      ]);

      // Fechar modal
      setIsConfirmModalOpen(false);
      setSelectedDelivery(null);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Erro ao confirmar entrega',
        description: error.message || 'Não foi possível confirmar a entrega'
      });
    } finally {
      setIsConfirming(false);
    }
  };

  // Atualizar dados
  const handleRefresh = () => {
    loadBalance();
    loadPendingDeliveries();
    loadMovements(pagination.page);
  };

  useEffect(() => {
    loadBalance();
    loadPendingDeliveries();
    loadMovements();
  }, []);

  // Formatar valor monetário
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Formatar data
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Meu Caixa</h1>
          <p className="text-muted-foreground">Gerencie o seu saldo e visualize o histórico de movimentações</p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Atualizar
        </Button>
      </div>

      {/* Card de Saldo */}
      <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Wallet className="mr-2 h-5 w-5" />
            Saldo Disponível
          </CardTitle>
          <CardDescription className="text-blue-100">
            Valor total disponível para entregas
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingBalance ? (
            <div className="text-3xl font-bold">Carregando...</div>
          ) : (
            <>
              <div className="text-4xl font-bold mb-2">
                {balance ? formatCurrency(parseFloat(balance.current_balance)) : 'AOA 0,00'}
              </div>
              {balance && balance.branch_name && (
                <div className="text-sm text-blue-100">
                  Agência: {balance.branch_name} {balance.branch_code && `(${balance.branch_code})`}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Entregas Pendentes de Confirmação */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-5 w-5" />
            Entregas Pendentes de Confirmação
          </CardTitle>
          <CardDescription>
            Entregas aguardando a sua confirmação
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingDeliveries ? (
            <div className="text-center py-8 text-muted-foreground">Carregando entregas...</div>
          ) : pendingDeliveries.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="mx-auto h-12 w-12 mb-4 text-green-500" />
              <p>Não há entregas pendentes de confirmação</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingDeliveries.map((delivery) => (
                <div key={delivery.id} className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-2">
                        <div className="text-lg font-semibold text-green-600">
                          {formatCurrency(delivery.amount)}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {delivery.source_name}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {delivery.delivered_by.role}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-muted-foreground">
                        <div>
                          <strong>Data/Hora:</strong> {formatDate(delivery.created_at)}
                        </div>
                        <div>
                          <strong>Operador:</strong> {delivery.delivered_by.name}
                        </div>
                        <div>
                          <strong>Agência:</strong> {delivery.branch?.name || 'N/A'}
                        </div>
                      </div>

                      {delivery.notes && (
                        <div className="mt-2 text-sm text-muted-foreground">
                          <strong>Observações:</strong> {delivery.notes}
                        </div>
                      )}
                    </div>

                    <Button
                      onClick={() => {
                        setSelectedDelivery(delivery);
                        setConfirmedAmount(delivery.amount.toString());
                        setObservations('');
                        setIsConfirmModalOpen(true);
                      }}
                      className="ml-4"
                      size="sm"
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Ver e Confirmar
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="movement_type">Tipo de Movimento</Label>
              <Select
                value={filters.movement_type}
                onValueChange={(value) => setFilters({ ...filters, movement_type: value })}
              >
                <SelectTrigger id="movement_type">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="credit">Crédito (Entrada)</SelectItem>
                  <SelectItem value="debit">Débito (Saída)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="start_date">Data Inicial</Label>
              <Input
                id="start_date"
                type="date"
                value={filters.start_date}
                onChange={(e) => setFilters({ ...filters, start_date: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">Data Final</Label>
              <Input
                id="end_date"
                type="date"
                value={filters.end_date}
                onChange={(e) => setFilters({ ...filters, end_date: e.target.value })}
              />
            </div>

            <div className="space-y-2 flex items-end gap-2">
              <Button onClick={handleApplyFilters} className="flex-1">
                Aplicar
              </Button>
              <Button onClick={handleClearFilters} variant="outline">
                Limpar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Movimentações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Histórico de Movimentações
          </CardTitle>
          <CardDescription>
            Total de {pagination.total} movimentação(ões) encontrada(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingMovements ? (
            <div className="text-center py-8 text-muted-foreground">Carregando movimentações...</div>
          ) : movements.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">Nenhuma movimentação encontrada</div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data/Hora</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Descrição</TableHead>
                      <TableHead>Origem</TableHead>
                      <TableHead className="text-right">Saldo Anterior</TableHead>
                      <TableHead className="text-right">Valor</TableHead>
                      <TableHead className="text-right">Saldo Posterior</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {movements.map((movement) => (
                      <TableRow key={movement.id}>
                        <TableCell className="font-medium">
                          {formatDate(movement.created_at)}
                        </TableCell>
                        <TableCell>
                          {movement.movement_type === 'credit' ? (
                            <Badge className="bg-green-500 hover:bg-green-600">
                              <TrendingUp className="mr-1 h-3 w-3" />
                              Crédito
                            </Badge>
                          ) : (
                            <Badge className="bg-red-500 hover:bg-red-600">
                              <TrendingDown className="mr-1 h-3 w-3" />
                              Débito
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {movement.description}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{movement.source_type}</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(parseFloat(movement.balance_before))}
                        </TableCell>
                        <TableCell className={`text-right font-semibold ${
                          movement.movement_type === 'credit' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {movement.movement_type === 'credit' ? '+' : '-'}
                          {formatCurrency(parseFloat(movement.amount))}
                        </TableCell>
                        <TableCell className="text-right font-semibold">
                          {formatCurrency(parseFloat(movement.balance_after))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Paginação */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Página {pagination.page} de {pagination.totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadMovements(pagination.page - 1)}
                      disabled={pagination.page === 1}
                    >
                      Anterior
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadMovements(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                    >
                      Próxima
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Modal de Confirmação de Entrega */}
      <Dialog open={isConfirmModalOpen} onOpenChange={(open) => {
        setIsConfirmModalOpen(open);
        if (!open) {
          setSelectedDelivery(null);
          setConfirmedAmount('');
          setObservations('');
        }
      }}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmar Entrega</DialogTitle>
            <DialogDescription>
              Confirme os detalhes da entrega e ajuste o valor se necessário
            </DialogDescription>
          </DialogHeader>

          {selectedDelivery && (
            <div className="space-y-4">
              {/* Detalhes da Entrega */}
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Valor Declarado:</span>
                  <span className="font-bold text-green-600">
                    {formatCurrency(selectedDelivery.amount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Origem:</span>
                  <span>{selectedDelivery.source_name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Operador:</span>
                  <span>{selectedDelivery.delivered_by.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Data:</span>
                  <span>{formatDate(selectedDelivery.created_at)}</span>
                </div>
              </div>

              {/* Valor Confirmado */}
              <div className="space-y-2">
                <Label htmlFor="confirmed_amount">Valor Confirmado (AOA)</Label>
                <Input
                  id="confirmed_amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={confirmedAmount}
                  onChange={(e) => setConfirmedAmount(e.target.value)}
                  placeholder={selectedDelivery.amount.toString()}
                />
                {confirmedAmount && parseFloat(confirmedAmount) !== selectedDelivery.amount && (
                  <div className="text-sm text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-2 rounded">
                    <strong>Diferença detectada:</strong> {
                      parseFloat(confirmedAmount) > selectedDelivery.amount ? '+' : ''
                    }{(parseFloat(confirmedAmount) - selectedDelivery.amount).toFixed(2)} AOA
                  </div>
                )}
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="observations">Observações (opcional)</Label>
                <Textarea
                  id="observations"
                  value={observations}
                  onChange={(e) => setObservations(e.target.value)}
                  placeholder="Adicione observações sobre a confirmação..."
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmModalOpen(false)}
              disabled={isConfirming}
            >
              Cancelar
            </Button>
            <Button
              onClick={() => {
                const amount = parseFloat(confirmedAmount) || selectedDelivery?.amount || 0;
                handleConfirmDelivery({
                  confirmed_amount: amount,
                  observations: observations.trim() || undefined
                });
              }}
              disabled={isConfirming || !confirmedAmount || parseFloat(confirmedAmount) <= 0}
            >
              {isConfirming ? 'Confirmando...' : 'Confirmar Entrega'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MeuCaixa;

