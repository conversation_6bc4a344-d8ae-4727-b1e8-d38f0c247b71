const express = require('express');
const Joi = require('joi');
const { catchAsync, AppError } = require('../core/errorHandler');
const { authenticate, authorize } = require('../auth/middleware');
const notificationService = require('../notifications/notificationService');
const logger = require('../core/logger');

const router = express.Router();

// Aplicar autenticação a todas as rotas
router.use(authenticate);

// Esquemas de validação
const createNotificationSchema = Joi.object({
  typeCode: Joi.string().required(),
  title: Joi.string().min(1).max(255).required(),
  message: Joi.string().min(1).required(),
  data: Joi.object().optional(),
  targetUsers: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ).optional(),
  targetRoles: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ).optional()
});

const markAsReadSchema = Joi.object({
  notificationIds: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ).required()
});

/**
 * GET /api/notifications
 * Obter notificações do utilizador autenticado
 */
router.get('/', catchAsync(async (req, res) => {
  const userId = req.user.id;
  const {
    page = 1,
    limit = 20,
    unread_only = false,
    type_code = null
  } = req.query;

  const options = {
    page: parseInt(page),
    limit: Math.min(parseInt(limit), 100), // Máximo 100 por página
    unreadOnly: unread_only === 'true',
    typeCode: type_code || null
  };

  const result = await notificationService.getUserNotifications(userId, options);

  res.status(200).json({
    status: 'success',
    data: result
  });
}));

/**
 * GET /api/notifications/unread-count
 * Obter contagem de notificações não lidas
 */
router.get('/unread-count', catchAsync(async (req, res) => {
  const userId = req.user.id;
  const count = await notificationService.getUnreadCount(userId);

  res.status(200).json({
    status: 'success',
    data: { count }
  });
}));

/**
 * POST /api/notifications/mark-read
 * Marcar notificação(ões) como lida(s)
 */
router.post('/mark-read', catchAsync(async (req, res) => {
  const { error, value } = markAsReadSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      status: 'error',
      message: error.details[0].message
    });
  }

  const userId = req.user.id;
  const { notificationIds } = value;
  const ids = Array.isArray(notificationIds) ? notificationIds : [notificationIds];

  let markedCount = 0;
  for (const id of ids) {
    const success = await notificationService.markAsRead(id, userId);
    if (success) markedCount++;
  }

  res.status(200).json({
    status: 'success',
    data: {
      requested: ids.length,
      marked: markedCount
    }
  });
}));

/**
 * POST /api/notifications/mark-all-read
 * Marcar todas as notificações como lidas
 */
router.post('/mark-all-read', catchAsync(async (req, res) => {
  const userId = req.user.id;
  const markedCount = await notificationService.markAllAsRead(userId);

  res.status(200).json({
    status: 'success',
    data: { marked: markedCount }
  });
}));

/**
 * POST /api/notifications/create
 * Criar nova notificação (apenas Admin e Gerente)
 */
router.post('/create', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const { error, value } = createNotificationSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      status: 'error',
      message: error.details[0].message
    });
  }

  const result = await notificationService.createNotification(value);

  logger.info(`Notificação criada manualmente por ${req.user.email}`, {
    typeCode: value.typeCode,
    title: value.title,
    createdBy: req.user.id,
    recipientCount: result.count
  });

  res.status(201).json({
    status: 'success',
    data: result
  });
}));

/**
 * GET /api/notifications/types
 * Listar tipos de notificação disponíveis (Admin e Gerente)
 */
router.get('/types', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const { executeQuery } = require('../config/database');
  
  const types = await executeQuery(
    `SELECT id, code, name, description, target_roles, is_active, created_at
     FROM notification_types
     ORDER BY name`
  );

  const formattedTypes = types.map(type => ({
    ...type,
    target_roles: JSON.parse(type.target_roles)
  }));

  res.status(200).json({
    status: 'success',
    data: { types: formattedTypes }
  });
}));

/**
 * GET /api/notifications/stats
 * Estatísticas de notificações (Admin e Gerente)
 */
router.get('/stats', authorize('admin', 'gerente'), catchAsync(async (req, res) => {
  const { executeQuery } = require('../config/database');
  
  // Estatísticas gerais
  const [totalStats, unreadStats, typeStats] = await Promise.all([
    // Total de notificações
    executeQuery(`
      SELECT COUNT(*) as total_notifications,
             COUNT(CASE WHEN read_at IS NULL THEN 1 END) as total_unread
      FROM notifications
    `),
    
    // Notificações não lidas por utilizador
    executeQuery(`
      SELECT u.full_name, u.email, r.name as role_name,
             COUNT(*) as unread_count
      FROM notifications n
      JOIN users u ON n.user_id = u.id
      JOIN roles r ON u.role_id = r.id
      WHERE n.read_at IS NULL
      GROUP BY u.id, u.full_name, u.email, r.name
      ORDER BY unread_count DESC
      LIMIT 10
    `),
    
    // Notificações por tipo (últimos 30 dias)
    executeQuery(`
      SELECT nt.name, nt.code, COUNT(*) as count
      FROM notifications n
      JOIN notification_types nt ON n.type_id = nt.id
      WHERE n.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY nt.id, nt.name, nt.code
      ORDER BY count DESC
    `)
  ]);

  res.status(200).json({
    status: 'success',
    data: {
      overview: totalStats[0],
      unread_by_user: unreadStats,
      notifications_by_type: typeStats
    }
  });
}));

module.exports = router;
