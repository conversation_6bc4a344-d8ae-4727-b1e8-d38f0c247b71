const { v4: uuidv4 } = require('uuid');
const { executeQuery } = require('../config/database');
const logger = require('../core/logger');

/**
 * Serviço de Notificações
 * Responsável por criar, gerir e enviar notificações baseadas em perfis
 */
class NotificationService {
  
  /**
   * Criar uma nova notificação
   * @param {Object} params - Parâmetros da notificação
   * @param {string} params.typeCode - Código do tipo de notificação
   * @param {string} params.title - Título da notificação
   * @param {string} params.message - Mensagem da notificação
   * @param {Object} params.data - Dados adicionais (opcional)
   * @param {string|Array} params.targetUsers - ID do utilizador ou array de IDs (opcional)
   * @param {string|Array} params.targetRoles - Role ou array de roles (opcional)
   * @returns {Promise<Object>} Resultado da criação
   */
  async createNotification({ typeCode, title, message, data = null, targetUsers = null, targetRoles = null }) {
    try {
      // 1. Obter tipo de notificação
      const notificationTypes = await executeQuery(
        'SELECT id, target_roles FROM notification_types WHERE code = ? AND is_active = true',
        [typeCode]
      );

      if (!notificationTypes.length) {
        throw new Error(`Tipo de notificação '${typeCode}' não encontrado ou inativo`);
      }

      const notificationType = notificationTypes[0];
      const allowedRoles = JSON.parse(notificationType.target_roles);

      // 2. Determinar utilizadores alvo
      let userIds = [];

      if (targetUsers) {
        // Se utilizadores específicos foram fornecidos
        userIds = Array.isArray(targetUsers) ? targetUsers : [targetUsers];
      } else if (targetRoles) {
        // Se roles foram fornecidos, obter utilizadores desses roles
        const roles = Array.isArray(targetRoles) ? targetRoles : [targetRoles];
        const validRoles = roles.filter(role => allowedRoles.includes(role));
        
        if (validRoles.length > 0) {
          const placeholders = validRoles.map(() => '?').join(',');
          const users = await executeQuery(
            `SELECT DISTINCT u.id 
             FROM users u 
             JOIN roles r ON u.role_id = r.id 
             WHERE r.name IN (${placeholders}) AND u.is_active = true`,
            validRoles
          );
          userIds = users.map(user => user.id);
        }
      } else {
        // Se nenhum alvo específico, usar todos os utilizadores dos roles permitidos
        const placeholders = allowedRoles.map(() => '?').join(',');
        const users = await executeQuery(
          `SELECT DISTINCT u.id 
           FROM users u 
           JOIN roles r ON u.role_id = r.id 
           WHERE r.name IN (${placeholders}) AND u.is_active = true`,
          allowedRoles
        );
        userIds = users.map(user => user.id);
      }

      // 3. Criar notificações para cada utilizador
      const notifications = [];
      for (const userId of userIds) {
        const notificationId = uuidv4();
        
        await executeQuery(
          `INSERT INTO notifications (id, user_id, type_id, title, message, data, created_at)
           VALUES (?, ?, ?, ?, ?, ?, NOW())`,
          [notificationId, userId, notificationType.id, title, message, data ? JSON.stringify(data) : null]
        );

        notifications.push({
          id: notificationId,
          user_id: userId,
          type_code: typeCode,
          title,
          message
        });
      }

      logger.info(`Notificação '${typeCode}' criada para ${userIds.length} utilizador(es)`, {
        typeCode,
        title,
        userCount: userIds.length,
        userIds
      });

      return {
        success: true,
        count: notifications.length,
        notifications
      };

    } catch (error) {
      logger.error('Erro ao criar notificação:', {
        error: error.message,
        typeCode,
        title
      });
      throw error;
    }
  }

  /**
   * Obter notificações de um utilizador
   * @param {string} userId - ID do utilizador
   * @param {Object} options - Opções de consulta
   * @returns {Promise<Object>} Notificações e metadados
   */
  async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        unreadOnly = false,
        typeCode = null
      } = options;

      const offset = (page - 1) * limit;
      let whereConditions = ['n.user_id = ?'];
      let params = [userId];

      if (unreadOnly) {
        whereConditions.push('n.read_at IS NULL');
      }

      if (typeCode) {
        whereConditions.push('nt.code = ?');
        params.push(typeCode);
      }

      const whereClause = whereConditions.join(' AND ');

      // Consulta principal
      const notifications = await executeQuery(
        `SELECT n.id, n.title, n.message, n.data, n.read_at, n.created_at,
                nt.code as type_code, nt.name as type_name
         FROM notifications n
         JOIN notification_types nt ON n.type_id = nt.id
         WHERE ${whereClause}
         ORDER BY n.created_at DESC
         LIMIT ? OFFSET ?`,
        [...params, limit, offset]
      );

      // Contar total
      const totalResult = await executeQuery(
        `SELECT COUNT(*) as total
         FROM notifications n
         JOIN notification_types nt ON n.type_id = nt.id
         WHERE ${whereClause}`,
        params
      );

      const total = totalResult[0].total;
      const totalPages = Math.ceil(total / limit);

      return {
        notifications: notifications.map(n => ({
          ...n,
          data: n.data ? JSON.parse(n.data) : null,
          is_read: !!n.read_at
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };

    } catch (error) {
      logger.error('Erro ao obter notificações do utilizador:', {
        error: error.message,
        userId
      });
      throw error;
    }
  }

  /**
   * Marcar notificação como lida
   * @param {string} notificationId - ID da notificação
   * @param {string} userId - ID do utilizador (para validação)
   * @returns {Promise<boolean>} Sucesso da operação
   */
  async markAsRead(notificationId, userId) {
    try {
      const result = await executeQuery(
        'UPDATE notifications SET read_at = NOW() WHERE id = ? AND user_id = ? AND read_at IS NULL',
        [notificationId, userId]
      );

      return result.affectedRows > 0;
    } catch (error) {
      logger.error('Erro ao marcar notificação como lida:', {
        error: error.message,
        notificationId,
        userId
      });
      throw error;
    }
  }

  /**
   * Marcar todas as notificações de um utilizador como lidas
   * @param {string} userId - ID do utilizador
   * @returns {Promise<number>} Número de notificações marcadas
   */
  async markAllAsRead(userId) {
    try {
      const result = await executeQuery(
        'UPDATE notifications SET read_at = NOW() WHERE user_id = ? AND read_at IS NULL',
        [userId]
      );

      return result.affectedRows;
    } catch (error) {
      logger.error('Erro ao marcar todas as notificações como lidas:', {
        error: error.message,
        userId
      });
      throw error;
    }
  }

  /**
   * Obter contagem de notificações não lidas
   * @param {string} userId - ID do utilizador
   * @returns {Promise<number>} Número de notificações não lidas
   */
  async getUnreadCount(userId) {
    try {
      const result = await executeQuery(
        'SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND read_at IS NULL',
        [userId]
      );

      return result[0].count;
    } catch (error) {
      logger.error('Erro ao obter contagem de notificações não lidas:', {
        error: error.message,
        userId
      });
      throw error;
    }
  }

  /**
   * Criar notificação de entrega pendente para tesoureiro
   * @param {Object} delivery - Dados da entrega
   * @returns {Promise<Object>} Resultado da criação
   */
  async createTreasuryDeliveryPendingNotification(delivery) {
    const { amount, delivered_by_name, source_name } = delivery;
    
    return this.createNotification({
      typeCode: 'TREASURY_DELIVERY_PENDING',
      title: 'Nova Entrega Aguarda Confirmação',
      message: `Nova entrega de ${this.formatCurrency(amount)} do ${source_name} aguarda a sua confirmação.`,
      data: {
        delivery_id: delivery.id,
        amount: delivery.amount,
        source: source_name,
        delivered_by: delivered_by_name
      },
      targetRoles: ['tesoureiro']
    });
  }

  /**
   * Criar notificação de entrega confirmada
   * @param {Object} delivery - Dados da entrega
   * @param {string} confirmedBy - Nome do tesoureiro que confirmou
   * @returns {Promise<Object>} Resultado da criação
   */
  async createTreasuryDeliveryConfirmedNotification(delivery, confirmedBy) {
    const { amount, delivered_by } = delivery;
    
    return this.createNotification({
      typeCode: 'TREASURY_DELIVERY_CONFIRMED',
      title: 'Entrega Confirmada',
      message: `A sua entrega de ${this.formatCurrency(amount)} foi confirmada pelo tesoureiro ${confirmedBy}.`,
      data: {
        delivery_id: delivery.id,
        amount: delivery.amount,
        confirmed_by: confirmedBy
      },
      targetUsers: [delivered_by]
    });
  }

  /**
   * Criar notificação de alerta de diferença de caixa
   * @param {Object} alert - Dados do alerta
   * @returns {Promise<Object>} Resultado da criação
   */
  async createCashDifferenceAlert(alert) {
    const { difference_amount, operator_name, source_name } = alert;
    
    return this.createNotification({
      typeCode: 'CASH_DIFFERENCE_ALERT',
      title: 'Alerta: Diferença de Caixa Detectada',
      message: `Diferença de ${this.formatCurrency(Math.abs(difference_amount))} detectada na entrega do ${source_name} (${operator_name}).`,
      data: {
        alert_id: alert.id,
        delivery_id: alert.delivery_id,
        difference_amount: alert.difference_amount,
        operator: operator_name,
        source: source_name
      },
      targetRoles: ['gerente', 'admin']
    });
  }

  /**
   * Formatar valor monetário
   * @param {number} value - Valor a formatar
   * @returns {string} Valor formatado
   */
  formatCurrency(value) {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(value);
  }
}

module.exports = new NotificationService();
