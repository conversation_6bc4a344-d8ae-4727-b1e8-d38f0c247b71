
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { ConfirmDialogProvider } from "@/contexts/ConfirmDialogContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { AuthProvider } from "@/contexts/AuthContext";
import Layout from "./components/layout/Layout";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import TransferenciaInterna from "./pages/Transferencias/Interna";
import TransferenciaSPTR from "./pages/Transferencias/SPTR";
import TransferenciaSTC from "./pages/Transferencias/STC";
import ConsultarTransferencias from "./pages/Transferencias/ConsultarTransferencias";
import RegistarBalcao from "./pages/Sistema/RegistarBalcao";
import RegistarUsuario from "./pages/Sistema/RegistarUsuario";
import ListarUsuario from "./pages/Sistema/ListarUsuario";

import SaldoInicial from "./pages/Sistema/SaldoInicial";
import Caixas from "./pages/Sistema/Caixas";
import DefinirTarefas from "./pages/Sistema/DefinirTarefas";
import DataSistema from "./pages/Sistema/DataSistema";
import EntregaTesoureiro from "./pages/Tesouraria/EntregaTesoureiro";
import EntregaBalcao from "./pages/Sistema/EntregaBalcao";
import BalcaoEntregaTesoureiro from "./pages/Balcao/EntregaTesoureiro";
import CaixasAbertos from "./pages/Sistema/CaixasAbertos";
import GestaoATMs from "./pages/Sistema/GestaoATMs";
import AbrirContaParticular from "./pages/Clientes/AbrirContaParticular";
import AbrirContaEmpresa from "./pages/Clientes/AbrirContaEmpresa";
import MovimentosSuspensos from "./pages/Transferencias/MovimentosSuspensos";
import AprovacaoContas from "./pages/Clientes/AprovacaoContas";
import GestaoClientes from "./pages/Clientes/GestaoClientes";
import GestaoContas from "./pages/Contas/GestaoContas";
import Caixa from "./pages/Caixa";
import AberturaCaixa from "./pages/caixa/AberturaCaixa";
import FechamentoCaixa from "./pages/caixa/FechamentoCaixa";
import DiferencasCaixa from "./pages/Caixa/DiferencasCaixa";
import EntregaCaixa from "./pages/Tesouraria/EntregaCaixa";
import EntregaCofre from "./pages/Tesouraria/EntregaCofre";
import GestaoCofre from "./pages/Tesouraria/GestaoCofre";
import MeuCaixa from "./pages/Tesouraria/MeuCaixa";
import CarregamentoATM from "./pages/Tesouraria/CarregamentoATM";
import Cartoes from "./pages/Cartoes";
import Cambios from "./pages/Cambios";
import Seguros from "./pages/Seguros";
import Sistema from "./pages/Sistema";
import ATM from "./pages/ATM";
import AuditPage from "./pages/AuditPage";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <AuthProvider>
        <NotificationProvider>
          <ConfirmDialogProvider>
            <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/*" element={
                <ProtectedRoute>
                  <Layout>
                    <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/clientes" element={<GestaoClientes />} />
            <Route path="/clientes/abrir-conta-particular" element={<AbrirContaParticular />} />
            <Route path="/clientes/abrir-conta-empresa" element={<AbrirContaEmpresa />} />
            <Route path="/contas/aprovacao-contas" element={<AprovacaoContas />} />
            <Route path="/contas/gestao-contas" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente', 'caixa']}>
                <GestaoContas />
              </ProtectedRoute>
            } />
            <Route path="/transferencias/movimentos-suspensos" element={<MovimentosSuspensos />} />
            <Route path="/caixa" element={<Caixa />} />
            <Route path="/caixa/abertura-caixa" element={<AberturaCaixa />} />
            <Route path="/caixa/fechamento-caixa" element={
              <ProtectedRoute requiredRoles={['caixa']}>
                <FechamentoCaixa />
              </ProtectedRoute>
            } />
            <Route path="/caixa/diferencas-caixa" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <DiferencasCaixa />
              </ProtectedRoute>
            } />
            <Route path="/tesouraria/meu-caixa" element={
              <ProtectedRoute requiredRoles={['tesoureiro']}>
                <MeuCaixa />
              </ProtectedRoute>
            } />
            <Route path="/tesouraria/gestao-cofre" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <GestaoCofre />
              </ProtectedRoute>
            } />
            <Route path="/tesouraria/entrega-caixa" element={<EntregaCaixa />} />
            <Route path="/tesouraria/entrega-cofre" element={<EntregaCofre />} />
            <Route path="/tesouraria/entrega-tesoureiro" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <EntregaTesoureiro />
              </ProtectedRoute>
            } />
            <Route path="/tesouraria/carregamento-atm" element={<CarregamentoATM />} />
            <Route path="/transferencias/interna" element={<TransferenciaInterna />} />
            <Route path="/transferencias/sptr" element={<TransferenciaSPTR />} />
            <Route path="/transferencias/stc" element={<TransferenciaSTC />} />
            <Route path="/transferencias/consultar" element={<ConsultarTransferencias />} />
            <Route path="/cartoes" element={<Cartoes />} />
            <Route path="/cambios" element={<Cambios />} />
            <Route path="/seguros" element={<Seguros />} />
            <Route path="/sistema" element={<Sistema />} />
            <Route path="/sistema/registar-balcao" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <RegistarBalcao />
              </ProtectedRoute>
            } />
            <Route path="/sistema/registar-usuario" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <RegistarUsuario />
              </ProtectedRoute>
            } />
            <Route path="/sistema/listar-usuario" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <ListarUsuario />
              </ProtectedRoute>
            } />

            <Route path="/sistema/saldo-inicial" element={
              <ProtectedRoute requiredRoles={['admin']}>
                <SaldoInicial />
              </ProtectedRoute>
            } />
            <Route path="/sistema/caixas" element={<Caixas />} />
            <Route path="/sistema/definir-tarefas" element={<DefinirTarefas />} />
            <Route path="/sistema/data-sistema" element={<DataSistema />} />
            <Route path="/sistema/entrega-tesoureiro" element={
              <ProtectedRoute requiredRoles={['balcao']}>
                <BalcaoEntregaTesoureiro />
              </ProtectedRoute>
            } />
            <Route path="/sistema/entrega-balcao" element={<EntregaBalcao />} />
            <Route path="/sistema/caixas-abertos" element={<CaixasAbertos />} />
            <Route path="/sistema/gestao-atms" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <GestaoATMs />
              </ProtectedRoute>
            } />
            <Route path="/sistema/auditoria" element={
              <ProtectedRoute requiredRoles={['admin', 'gerente']}>
                <AuditPage />
              </ProtectedRoute>
            } />
            <Route path="/atm" element={<ATM />} />
                      {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Layout>
                </ProtectedRoute>
              } />
            </Routes>
          </BrowserRouter>
          </TooltipProvider>
          </ConfirmDialogProvider>
        </NotificationProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
