-- =============================================
-- MIGRAÇÃO: Sistema de Notificações
-- Data: 09/10/2025
-- Descrição: Adiciona tabelas para sistema completo de notificações
-- =============================================

-- Tabela de tipos de notificação
CREATE TABLE IF NOT EXISTS `notification_types` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code` VARCHAR(50) UNIQUE NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `target_roles` JSON NOT NULL COMMENT 'Array de roles que podem receber este tipo de notificação',
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_notification_types_code` (`code`),
    INDEX `idx_notification_types_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela principal de notificações
CREATE TABLE IF NOT EXISTS `notifications` (
    `id` CHAR(36) PRIMARY KEY,
    `user_id` CHAR(36) NOT NULL,
    `type_id` INT NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `message` TEXT NOT NULL,
    `data` JSON NULL COMMENT 'Dados adicionais específicos da notificação',
    `read_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`type_id`) REFERENCES `notification_types`(`id`),
    INDEX `idx_notifications_user` (`user_id`),
    INDEX `idx_notifications_type` (`type_id`),
    INDEX `idx_notifications_read` (`read_at`),
    INDEX `idx_notifications_created` (`created_at`),
    INDEX `idx_notifications_user_unread` (`user_id`, `read_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de alertas de diferença de caixa
CREATE TABLE IF NOT EXISTS `cash_difference_alerts` (
    `id` CHAR(36) PRIMARY KEY,
    `delivery_id` CHAR(36) NOT NULL,
    `cash_register_id` INT NULL,
    `operator_id` CHAR(36) NOT NULL COMMENT 'ID do operador que fez a entrega',
    `treasurer_id` CHAR(36) NOT NULL COMMENT 'ID do tesoureiro que confirmou',
    `declared_amount` DECIMAL(15,2) NOT NULL,
    `confirmed_amount` DECIMAL(15,2) NOT NULL,
    `difference_amount` DECIMAL(15,2) NOT NULL,
    `status` ENUM('pending', 'resolved', 'dismissed') DEFAULT 'pending',
    `resolution_notes` TEXT NULL,
    `resolved_by` CHAR(36) NULL,
    `resolved_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`delivery_id`) REFERENCES `treasury_deliveries`(`id`),
    FOREIGN KEY (`cash_register_id`) REFERENCES `cash_registers`(`id`),
    FOREIGN KEY (`operator_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`treasurer_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`),
    INDEX `idx_cash_difference_delivery` (`delivery_id`),
    INDEX `idx_cash_difference_status` (`status`),
    INDEX `idx_cash_difference_operator` (`operator_id`),
    INDEX `idx_cash_difference_treasurer` (`treasurer_id`),
    INDEX `idx_cash_difference_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- SEED DOS TIPOS DE NOTIFICAÇÃO
-- =============================================

INSERT INTO `notification_types` (`code`, `name`, `description`, `target_roles`) VALUES
-- Notificações de Tesouraria
('TREASURY_DELIVERY_PENDING', 'Entrega Pendente para Tesoureiro', 'Nova entrega aguarda confirmação do tesoureiro', '["tesoureiro"]'),
('TREASURY_DELIVERY_CONFIRMED', 'Entrega Confirmada', 'Entrega foi confirmada pelo tesoureiro', '["caixa", "balcao"]'),
('TREASURY_SUPPLY_RECEIVED', 'Suprimento Recebido', 'Recebeu suprimento da tesouraria', '["caixa", "balcao"]'),

-- Alertas de Diferença
('CASH_DIFFERENCE_ALERT', 'Alerta de Diferença de Caixa', 'Diferença detectada entre valor declarado e confirmado', '["gerente", "admin"]'),

-- Aprovações de Conta
('ACCOUNT_APPROVAL_PENDING', 'Conta Aguarda Aprovação', 'Nova conta de cliente aguarda aprovação', '["gerente", "admin"]'),
('ACCOUNT_APPROVED', 'Conta Aprovada', 'Conta de cliente foi aprovada', '["caixa", "balcao"]'),
('ACCOUNT_REJECTED', 'Conta Rejeitada', 'Conta de cliente foi rejeitada', '["caixa", "balcao"]'),

-- Atualizações de Sistema
('EXCHANGE_RATE_UPDATED', 'Taxas de Câmbio Atualizadas', 'As taxas de câmbio foram atualizadas', '["tesoureiro", "caixa", "balcao", "gerente", "admin"]'),

-- Tipos Futuros (preparados para implementação posterior)
('CARD_APPROVAL_PENDING', 'Cartão Aguarda Aprovação', 'Novo cartão aguarda aprovação', '["gerente", "admin"]'),
('CARD_APPROVED', 'Cartão Aprovado', 'Cartão foi aprovado', '["caixa", "balcao"]'),
('CARD_REJECTED', 'Cartão Rejeitado', 'Cartão foi rejeitado', '["caixa", "balcao"]'),
('NEW_TASK_ASSIGNED', 'Nova Tarefa Atribuída', 'Nova tarefa foi atribuída', '["admin", "gerente", "tesoureiro", "caixa", "balcao", "tecnico"]'),
('TASK_STATUS_UPDATED', 'Status de Tarefa Atualizado', 'Status de uma tarefa foi atualizado', '["admin", "gerente", "tesoureiro", "caixa", "balcao", "tecnico"]');

-- =============================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =============================================

/*
ESTRUTURA DO SISTEMA DE NOTIFICAÇÕES:

1. notification_types: Define os tipos de notificação disponíveis
   - code: Código único para identificar o tipo
   - target_roles: JSON array com os roles que podem receber este tipo
   - is_active: Permite desativar tipos de notificação

2. notifications: Armazena as notificações individuais
   - user_id: Utilizador que recebe a notificação
   - type_id: Referência ao tipo de notificação
   - data: JSON com dados específicos (IDs, valores, etc.)
   - read_at: Timestamp quando foi lida (NULL = não lida)

3. cash_difference_alerts: Alertas específicos para diferenças de caixa
   - Armazena detalhes completos da diferença
   - Permite rastreamento de resolução
   - Ligado à entrega original (delivery_id)

ÍNDICES OTIMIZADOS:
- Consultas por utilizador e status de leitura
- Consultas por tipo de notificação
- Consultas por data de criação
- Consultas de alertas por status e operador
*/
