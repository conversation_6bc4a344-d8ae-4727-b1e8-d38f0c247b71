-- Tabela de tipos de notificação
CREATE TABLE IF NOT EXISTS `notification_types` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `code` VARCHAR(50) UNIQUE NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `target_roles` JSON NOT NULL COMMENT 'Array de roles que podem receber este tipo de notificação',
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_notification_types_code` (`code`),
    INDEX `idx_notification_types_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
