-- Tabela principal de notificações
CREATE TABLE IF NOT EXISTS `notifications` (
    `id` CHAR(36) PRIMARY KEY,
    `user_id` CHAR(36) NOT NULL,
    `type_id` INT NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `message` TEXT NOT NULL,
    `data` JSON NULL COMMENT 'Dados adicionais específicos da notificação',
    `read_at` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`type_id`) REFERENCES `notification_types`(`id`),
    INDEX `idx_notifications_user` (`user_id`),
    INDEX `idx_notifications_type` (`type_id`),
    INDEX `idx_notifications_read` (`read_at`),
    INDEX `idx_notifications_created` (`created_at`),
    INDEX `idx_notifications_user_unread` (`user_id`, `read_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
