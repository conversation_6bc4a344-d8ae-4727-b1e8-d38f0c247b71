const express = require('express');
const { catchAsync, AppError } = require('../core/errorHandler');
const { executeQuery } = require('../config/database');
const { authorize } = require('../auth/middleware');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const logger = require('../core/logger');

const router = express.Router();

// Schemas de validação
const deliverToCashSchema = Joi.object({
  cash_register_id: Joi.number().integer().positive().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

const deliverToVaultSchema = Joi.object({
  amount: Joi.number().positive().required(),
  source_type: Joi.string().valid('cash_register', 'treasury', 'manual', 'system').required(),
  source_id: Joi.string().allow('').max(50),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

const deliverToTreasurerSchema = Joi.object({
  treasurer_id: Joi.string().uuid().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

const deliverToCounterSchema = Joi.object({
  counter_id: Joi.string().required(),
  counter_name: Joi.string().required(),
  branch_name: Joi.string().required(),
  amount: Joi.number().positive().required(),
  denominations: Joi.object({
    notes_10000: Joi.number().integer().min(0).default(0),
    notes_5000: Joi.number().integer().min(0).default(0),
    notes_2000: Joi.number().integer().min(0).default(0),
    notes_1000: Joi.number().integer().min(0).default(0),
    notes_500: Joi.number().integer().min(0).default(0),
    notes_200: Joi.number().integer().min(0).default(0)
  }).required(),
  notes: Joi.string().allow('').max(500)
});

// Placeholder routes for treasury operations
// TODO: Implement full treasury functionality

/**
 * POST /api/treasury/deliver-to-cash
 * Entrega de valores da tesouraria para caixa
 */
router.post('/deliver-to-cash', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToCashSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { cash_register_id, amount, denominations, notes } = value;

  // 2. Verificar se caixa existe e está disponível
  const cashRegisters = await executeQuery(
    'SELECT id, register_number, status FROM cash_registers WHERE id = ?',
    [cash_register_id]
  );

  if (!cashRegisters || cashRegisters.length === 0) {
    return next(new AppError('Caixa não encontrado', 404, 'CASH_REGISTER_NOT_FOUND'));
  }

  if (cashRegisters[0].status !== 'available') {
    return next(new AppError('Caixa não está disponível para receber valores', 400, 'CASH_REGISTER_NOT_AVAILABLE'));
  }

  // 3. Calcular total das denominações
  const calculatedTotal = (
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200
  );

  // 4. Verificar se total das denominações confere com o valor informado
  if (Math.abs(calculatedTotal - amount) > 0.01) {
    return next(new AppError('Total das denominações não confere com o valor informado', 400, 'DENOMINATION_MISMATCH'));
  }

  // 5. Verificar saldo do tesoureiro (se for tesoureiro fazendo a entrega)
  const userId = req.user.id;
  const userRole = req.user.role_name;

  if (userRole === 'tesoureiro') {
    const treasurerBalances = await executeQuery(
      'SELECT id, current_balance FROM treasurer_balances WHERE user_id = ?',
      [userId]
    );

    if (!treasurerBalances || treasurerBalances.length === 0) {
      return next(new AppError('Saldo do tesoureiro não encontrado', 404, 'TREASURER_BALANCE_NOT_FOUND'));
    }

    const currentBalance = parseFloat(treasurerBalances[0].current_balance);
    if (currentBalance < amount) {
      return next(new AppError(
        `Saldo insuficiente. Disponível: ${currentBalance.toFixed(2)} AOA, Solicitado: ${amount.toFixed(2)} AOA`,
        400,
        'INSUFFICIENT_TREASURER_BALANCE'
      ));
    }
  }

  try {
    // 6. Iniciar transação
    await executeQuery('START TRANSACTION');

    // 7. Debitar saldo do tesoureiro (se aplicável)
    let treasurerBalanceBefore = 0;
    let treasurerBalanceAfter = 0;

    if (userRole === 'tesoureiro') {
      const balanceResult = await executeQuery(
        'SELECT current_balance FROM treasurer_balances WHERE user_id = ?',
        [userId]
      );
      treasurerBalanceBefore = parseFloat(balanceResult[0].current_balance);
      treasurerBalanceAfter = treasurerBalanceBefore - amount;

      await executeQuery(
        'UPDATE treasurer_balances SET current_balance = current_balance - ?, updated_at = NOW() WHERE user_id = ?',
        [amount, userId]
      );

      // Registrar movimento no histórico do tesoureiro
      await executeQuery(`
        INSERT INTO treasurer_movements (
          treasurer_id, movement_type, amount, balance_before, balance_after,
          source_type, source_id, reference_id, description, created_by, created_at
        ) VALUES (?, 'debit', ?, ?, ?, 'cash_register', ?, NULL, ?, ?, NOW())
      `, [
        userId,
        amount,
        treasurerBalanceBefore,
        treasurerBalanceAfter,
        cashRegisters[0].register_number,
        `Entrega ao caixa ${cashRegisters[0].register_number}`,
        userId
      ]);
    }

    // 8. Criar registro de entrega
    const deliveryId = uuidv4();

    await executeQuery(
      `INSERT INTO treasury_deliveries
       (id, cash_register_id, delivered_by, amount, denominations, notes, delivery_type, created_at)
       VALUES (?, ?, ?, ?, ?, ?, 'cash_delivery', NOW())`,
      [deliveryId, cash_register_id, req.user.id, amount, JSON.stringify(denominations), notes || null]
    );

    // 9. Confirmar transação
    await executeQuery('COMMIT');

    // 10. Buscar informações completas da entrega
    const delivery = await executeQuery(
      `SELECT td.id, td.amount, td.denominations, td.notes, td.created_at,
              cr.register_number, cr.description as cash_register_description,
              u.full_name as delivered_by_name
       FROM treasury_deliveries td
       JOIN cash_registers cr ON td.cash_register_id = cr.id
       JOIN users u ON td.delivered_by = u.id
       WHERE td.id = ?`,
      [deliveryId]
    );

    logger.info(`Entrega a caixa realizada: ${amount} AOA para ${cashRegisters[0].register_number}`, {
      deliveryId,
      cashRegisterId: cash_register_id,
      amount,
      deliveredBy: req.user.id,
      treasurerDebit: userRole === 'tesoureiro' ? amount : 0
    });

    res.status(201).json({
      status: 'success',
      message: 'Entrega a caixa realizada com sucesso',
      data: {
        delivery: {
          ...delivery[0],
          denominations: JSON.parse(delivery[0].denominations)
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao caixa:', error);
    throw error;
  }
}));

/**
 * POST /api/treasury/deliver-to-vault
 * Entrega de valores ao cofre principal
 */
router.post('/deliver-to-vault', authorize('tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToVaultSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { amount, source_type, source_id, denominations, notes } = value;
  const userId = req.user.id;

  // 2. Verificar se existe cofre principal ativo
  const vaults = await executeQuery(
    'SELECT id, vault_code, vault_name, current_balance, capacity FROM main_vaults WHERE is_active = 1 LIMIT 1'
  );

  if (!vaults || vaults.length === 0) {
    return next(new AppError('Nenhum cofre principal ativo encontrado', 404, 'VAULT_NOT_FOUND'));
  }

  const vault = vaults[0];

  // 3. Verificar se o cofre não excederá a capacidade máxima
  const newBalance = parseFloat(vault.current_balance) + amount;
  if (newBalance > parseFloat(vault.capacity)) {
    return next(new AppError(
      `Operação excede a capacidade máxima do cofre (${vault.capacity} AOA)`,
      400,
      'VAULT_CAPACITY_EXCEEDED'
    ));
  }

  // 4. Validar denominações (total deve corresponder ao valor)
  const denominationsTotal =
    denominations.notes_10000 * 10000 +
    denominations.notes_5000 * 5000 +
    denominations.notes_2000 * 2000 +
    denominations.notes_1000 * 1000 +
    denominations.notes_500 * 500 +
    denominations.notes_200 * 200;

  if (Math.abs(denominationsTotal - amount) > 0.01) {
    return next(new AppError(
      'O total das denominações não corresponde ao valor informado',
      400,
      'DENOMINATIONS_MISMATCH'
    ));
  }

  // 5. Verificar saldo do tesoureiro
  const treasurerBalances = await executeQuery(
    'SELECT id, current_balance FROM treasurer_balances WHERE user_id = ?',
    [userId]
  );

  if (!treasurerBalances || treasurerBalances.length === 0) {
    return next(new AppError('Saldo do tesoureiro não encontrado', 404, 'TREASURER_BALANCE_NOT_FOUND'));
  }

  const currentBalance = parseFloat(treasurerBalances[0].current_balance);
  if (currentBalance < amount) {
    return next(new AppError(
      `Saldo insuficiente. Disponível: ${currentBalance.toFixed(2)} AOA, Solicitado: ${amount.toFixed(2)} AOA`,
      400,
      'INSUFFICIENT_TREASURER_BALANCE'
    ));
  }

  const referenceNumber = `VLT-${Date.now()}`;

  try {
    // 6. Iniciar transação
    await executeQuery('START TRANSACTION');

    // 7. Debitar saldo do tesoureiro
    const treasurerBalanceBefore = currentBalance;
    const treasurerBalanceAfter = treasurerBalanceBefore - amount;

    await executeQuery(
      'UPDATE treasurer_balances SET current_balance = current_balance - ?, updated_at = NOW() WHERE user_id = ?',
      [amount, userId]
    );

    // Registrar movimento no histórico do tesoureiro
    await executeQuery(`
      INSERT INTO treasurer_movements (
        treasurer_id, movement_type, amount, balance_before, balance_after,
        source_type, source_id, reference_id, description, created_by, created_at
      ) VALUES (?, 'debit', ?, ?, ?, 'vault', ?, NULL, ?, ?, NOW())
    `, [
      userId,
      amount,
      treasurerBalanceBefore,
      treasurerBalanceAfter,
      vault.vault_code,
      `Entrega ao cofre principal - Ref: ${referenceNumber}`,
      userId
    ]);

    // 8. Registrar movimento no cofre
    await executeQuery(
      `INSERT INTO vault_movements (
        vault_id, movement_type, amount, previous_balance, new_balance,
        source_type, source_id, reference_number, description, notes,
        denominations, user_id, processed_by
      ) VALUES (?, 'deposit', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        vault.id,
        amount,
        vault.current_balance,
        newBalance,
        source_type,
        source_id || null,
        referenceNumber,
        `Entrega ao cofre - ${source_type}`,
        notes || null,
        JSON.stringify(denominations),
        userId,
        userId
      ]
    );

    // 9. Atualizar saldo do cofre
    await executeQuery(
      'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
      [newBalance, vault.id]
    );

    // 10. Registrar na auditoria
    await executeQuery(
      `INSERT INTO audit_logs (
        user_id, action, table_name, record_id, new_values, ip_address, created_at
      ) VALUES (?, 'VAULT_DEPOSIT', 'vault_movements', ?, ?, ?, NOW())`,
      [
        userId,
        referenceNumber,
        JSON.stringify({
          amount,
          source_type,
          source_id,
          vault_code: vault.vault_code,
          previous_balance: vault.current_balance,
          new_balance: newBalance,
          reference_number: referenceNumber
        }),
        req.ip || req.connection.remoteAddress
      ]
    );

    // 11. Confirmar transação
    await executeQuery('COMMIT');

    // 12. Buscar dados completos do movimento criado
    const movement = await executeQuery(
      `SELECT
        vm.id, vm.vault_id, vm.movement_type, vm.amount,
        vm.previous_balance, vm.new_balance, vm.denominations,
        vm.reference_number, vm.description, vm.notes,
        vm.source_type, vm.source_id, vm.created_at
      FROM vault_movements vm
      WHERE vm.reference_number = ?
      ORDER BY vm.created_at DESC
      LIMIT 1`,
      [referenceNumber]
    );

    logger.info(`Entrega ao cofre realizada: ${amount} AOA por usuário ${userId}`);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao cofre realizada com sucesso',
      data: {
        movement: {
          ...movement[0],
          denominations: JSON.parse(movement[0].denominations)
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao cofre:', error);
    throw error;
  }
}));

/**
 * POST /api/treasury/deliver-to-treasurer
 * Entrega de valores do cofre principal para tesoureiro
 */
router.post('/deliver-to-treasurer', authorize('admin', 'gerente'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToTreasurerSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { treasurer_id, amount, denominations, notes } = value;
  const userId = req.user.id;

  // 2. Verificar se tesoureiro existe e está ativo
  const treasurers = await executeQuery(`
    SELECT u.id, u.full_name, u.email, u.is_active, r.name as role_name, b.name as branch_name
    FROM users u
    JOIN roles r ON u.role_id = r.id
    LEFT JOIN branches b ON u.branch_id = b.id
    WHERE u.id = ? AND r.name = 'tesoureiro' AND u.is_active = 1
  `, [treasurer_id]);

  if (!treasurers || treasurers.length === 0) {
    return next(new AppError('Tesoureiro não encontrado ou inativo', 404, 'TREASURER_NOT_FOUND'));
  }

  const treasurer = treasurers[0];

  // 3. Verificar se existe cofre principal ativo
  const vaults = await executeQuery(
    'SELECT id, vault_code, vault_name, current_balance, capacity FROM main_vaults WHERE is_active = 1 LIMIT 1'
  );

  if (!vaults || vaults.length === 0) {
    return next(new AppError('Cofre principal não encontrado', 404, 'VAULT_NOT_FOUND'));
  }

  const vault = vaults[0];

  // 4. Verificar se há saldo suficiente no cofre
  if (parseFloat(vault.current_balance) < amount) {
    return next(new AppError(
      `Saldo insuficiente no cofre. Disponível: ${vault.current_balance} AOA, Solicitado: ${amount} AOA`,
      400,
      'INSUFFICIENT_VAULT_BALANCE'
    ));
  }

  // 5. Validar denominações (soma deve ser igual ao valor)
  const denominationTotal =
    (denominations.notes_10000 * 10000) +
    (denominations.notes_5000 * 5000) +
    (denominations.notes_2000 * 2000) +
    (denominations.notes_1000 * 1000) +
    (denominations.notes_500 * 500) +
    (denominations.notes_200 * 200);

  if (Math.abs(denominationTotal - amount) > 0.01) {
    return next(new AppError(
      `Valor das denominações (${denominationTotal}) não confere com o valor informado (${amount})`,
      400,
      'DENOMINATION_MISMATCH'
    ));
  }

  try {
    // 6. Iniciar transação
    await executeQuery('START TRANSACTION');

    const deliveryId = uuidv4();
    const referenceNumber = `TRS-${Date.now()}`;
    const previousBalance = parseFloat(vault.current_balance);
    const newBalance = previousBalance - amount;

    // 7. Registrar entrega na tabela treasury_deliveries
    await executeQuery(`
      INSERT INTO treasury_deliveries (
        id, vault_id, delivered_by, amount, denominations, notes,
        delivery_type, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'treasurer_delivery', 'pending', NOW())
    `, [
      deliveryId,
      vault.id,
      userId,
      amount,
      JSON.stringify(denominations),
      notes || null
    ]);

    // 8. Atualizar saldo do cofre
    await executeQuery(
      'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
      [newBalance, vault.id]
    );

    // 9. Registrar movimento no cofre
    await executeQuery(`
      INSERT INTO vault_movements (
        id, vault_id, movement_type, amount, previous_balance, new_balance,
        denominations, reference_number, description, notes,
        source_type, source_id, processed_by, created_at
      ) VALUES (?, ?, 'withdrawal', ?, ?, ?, ?, ?, ?, ?, 'treasurer', ?, ?, NOW())
    `, [
      uuidv4(),
      vault.id,
      amount,
      previousBalance,
      newBalance,
      JSON.stringify(denominations),
      referenceNumber,
      `Entrega ao tesoureiro: ${treasurer.full_name}`,
      notes || null,
      treasurer_id,
      userId
    ]);

    // 10. Creditar saldo do tesoureiro
    // Verificar se tesoureiro já tem registro de saldo
    const treasurerBalances = await executeQuery(
      'SELECT id, current_balance FROM treasurer_balances WHERE user_id = ?',
      [treasurer_id]
    );

    let treasurerBalanceBefore = 0;
    if (treasurerBalances && treasurerBalances.length > 0) {
      treasurerBalanceBefore = parseFloat(treasurerBalances[0].current_balance);
      // Atualizar saldo existente
      await executeQuery(
        'UPDATE treasurer_balances SET current_balance = current_balance + ?, updated_at = NOW() WHERE user_id = ?',
        [amount, treasurer_id]
      );
    } else {
      // Criar novo registro de saldo
      await executeQuery(
        'INSERT INTO treasurer_balances (user_id, current_balance, branch_id) VALUES (?, ?, ?)',
        [treasurer_id, amount, treasurer.branch_id || req.user.branch_id]
      );
    }

    const treasurerBalanceAfter = treasurerBalanceBefore + amount;

    // 11. Registrar movimento no histórico do tesoureiro
    await executeQuery(`
      INSERT INTO treasurer_movements (
        treasurer_id, movement_type, amount, balance_before, balance_after,
        source_type, source_id, reference_id, description, created_by, created_at
      ) VALUES (?, 'credit', ?, ?, ?, 'vault', ?, NULL, ?, ?, NOW())
    `, [
      treasurer_id,
      amount,
      treasurerBalanceBefore,
      treasurerBalanceAfter,
      vault.vault_code,
      `Recebimento do cofre principal - Ref: ${referenceNumber}`,
      userId
    ]);

    // 12. Registrar log de auditoria
    await executeQuery(`
      INSERT INTO audit_logs (
        user_id, action, table_name, record_id, ip_address,
        old_values, new_values, created_at
      ) VALUES (?, 'TREASURER_DELIVERY', 'treasury_deliveries', ?, ?, ?, ?, NOW())
    `, [
      userId,
      deliveryId,
      req.ip || '::1',
      JSON.stringify({ vault_balance: previousBalance }),
      JSON.stringify({
        amount,
        treasurer_id,
        treasurer_name: treasurer.full_name,
        vault_balance: newBalance,
        reference_number: referenceNumber
      })
    ]);

    // 13. Confirmar transação
    await executeQuery('COMMIT');

    // 14. Buscar dados da entrega criada
    const delivery = await executeQuery(`
      SELECT
        td.id, td.amount, td.denominations, td.notes, td.delivery_type,
        td.status, td.created_at, u.full_name as treasurer_name,
        u.email as treasurer_email, mv.vault_name
      FROM treasury_deliveries td
      JOIN users u ON u.id = ?
      JOIN main_vaults mv ON td.vault_id = mv.id
      WHERE td.id = ?
    `, [treasurer_id, deliveryId]);

    logger.info(`Entrega ao tesoureiro realizada: ${amount} AOA para ${treasurer.full_name} por usuário ${userId}`);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao tesoureiro realizada com sucesso',
      data: {
        delivery: {
          ...delivery[0],
          denominations: JSON.parse(delivery[0].denominations),
          reference_number: referenceNumber
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao tesoureiro:', error);
    throw error;
  }
}));

/**
 * POST /api/treasury/load-atm
 * Carregamento de ATM
 */
router.post('/load-atm', authorize('tesoureiro'), (req, res) => {
  res.status(201).json({
    status: 'success',
    message: 'Carregamento de ATM - Em desenvolvimento'
  });
});

/**
 * POST /api/treasury/deliver-to-counter
 * Entrega de valores do cofre principal para balcão
 */
router.post('/deliver-to-counter', authorize('admin', 'gerente', 'tesoureiro'), catchAsync(async (req, res, next) => {
  // 1. Validar dados de entrada
  const { error, value } = deliverToCounterSchema.validate(req.body);
  if (error) {
    return next(new AppError(error.details[0].message, 400, 'VALIDATION_ERROR'));
  }

  const { counter_id, counter_name, branch_name, amount, denominations, notes } = value;
  const userId = req.user.id;

  try {
    // 2. Verificar se o cofre principal existe e tem saldo suficiente
    const vault = await executeQuery(
      'SELECT * FROM main_vaults WHERE is_active = 1 LIMIT 1'
    );

    if (!vault || vault.length === 0) {
      return next(new AppError('Cofre principal não encontrado para esta agência', 404, 'VAULT_NOT_FOUND'));
    }

    const currentBalance = parseFloat(vault[0].current_balance);
    if (currentBalance < amount) {
      return next(new AppError(
        `Saldo insuficiente no cofre. Saldo atual: ${currentBalance.toFixed(2)} AOA, Valor solicitado: ${amount.toFixed(2)} AOA`,
        400,
        'INSUFFICIENT_BALANCE'
      ));
    }

    // 3. Validar denominações
    const denominationsTotal = Object.entries(denominations).reduce((total, [key, quantity]) => {
      const value = key.includes('notes_') ?
        parseInt(key.replace('notes_', '')) :
        parseInt(key.replace('coins_', ''));
      return total + (quantity * value);
    }, 0);

    if (Math.abs(denominationsTotal - amount) > 0.01) {
      return next(new AppError(
        `Total das denominações (${denominationsTotal.toFixed(2)}) não confere com o valor informado (${amount.toFixed(2)})`,
        400,
        'DENOMINATIONS_MISMATCH'
      ));
    }

    // 4. Iniciar transação
    await executeQuery('START TRANSACTION');

    const deliveryId = uuidv4();
    const referenceNumber = `CTR-${Date.now()}`;
    const previousBalance = parseFloat(vault[0].current_balance);
    const newBalance = previousBalance - amount;

    // 5. Registrar entrega na tabela treasury_deliveries
    await executeQuery(`
      INSERT INTO treasury_deliveries (
        id, vault_id, delivered_by, amount, denominations, notes,
        delivery_type, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, 'counter_delivery', 'confirmed', NOW())
    `, [
      deliveryId,
      vault[0].id,
      userId,
      amount,
      JSON.stringify(denominations),
      notes || null
    ]);

    // 6. Atualizar saldo do cofre
    await executeQuery(
      'UPDATE main_vaults SET current_balance = ?, updated_at = NOW() WHERE id = ?',
      [newBalance, vault[0].id]
    );

    // 7. Registrar movimento no cofre
    await executeQuery(`
      INSERT INTO vault_movements (
        vault_id, movement_type, amount, previous_balance, new_balance,
        source_type, source_id, reference_number, description, notes,
        denominations, processed_by, created_at
      ) VALUES (?, 'withdrawal', ?, ?, ?, 'counter', ?, ?, ?, ?, ?, ?, NOW())
    `, [
      vault[0].id,
      amount,
      previousBalance,
      newBalance,
      counter_id,
      referenceNumber,
      `Entrega ao balcão: ${counter_name} - ${branch_name}`,
      notes || null,
      JSON.stringify(denominations),
      userId
    ]);

    // 8. Registrar na auditoria
    await executeQuery(
      `INSERT INTO audit_logs (
        user_id, action, table_name, record_id, new_values, ip_address, created_at
      ) VALUES (?, 'COUNTER_DELIVERY', 'treasury_deliveries', ?, ?, ?, NOW())`,
      [
        userId,
        deliveryId,
        JSON.stringify({
          counter_id,
          counter_name,
          branch_name,
          amount,
          vault_code: vault[0].vault_code,
          previous_balance: previousBalance,
          new_balance: newBalance,
          reference_number: referenceNumber
        }),
        req.ip || req.connection.remoteAddress
      ]
    );

    // 9. Confirmar transação
    await executeQuery('COMMIT');

    // 10. Buscar dados da entrega criada
    const delivery = await executeQuery(`
      SELECT
        td.*,
        u.full_name as delivered_by_name,
        mv.vault_code,
        mv.vault_name
      FROM treasury_deliveries td
      JOIN users u ON u.id = ?
      JOIN main_vaults mv ON td.vault_id = mv.id
      WHERE td.id = ?
    `, [userId, deliveryId]);

    logger.info(`Entrega ao balcão realizada: ${amount} AOA para ${counter_name} por usuário ${userId}`);

    res.status(201).json({
      status: 'success',
      message: 'Entrega ao balcão realizada com sucesso',
      data: {
        delivery: {
          ...delivery[0],
          denominations: JSON.parse(delivery[0].denominations),
          reference_number: referenceNumber,
          counter_id,
          counter_name,
          branch_name
        }
      }
    });

  } catch (error) {
    // Rollback em caso de erro
    await executeQuery('ROLLBACK');
    logger.error('Erro ao processar entrega ao balcão:', error);
    throw error;
  }
}));

/**
 * GET /api/treasury/my-balance
 * Obter saldo atual do tesoureiro logado
 */
router.get('/my-balance', authorize('tesoureiro'), catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  // Buscar saldo do tesoureiro
  const balances = await executeQuery(
    `SELECT tb.id, tb.current_balance, tb.branch_id, tb.created_at, tb.updated_at,
            b.name as branch_name, b.code as branch_code
     FROM treasurer_balances tb
     LEFT JOIN branches b ON tb.branch_id = b.id
     WHERE tb.user_id = ?`,
    [userId]
  );

  if (!balances || balances.length === 0) {
    // Criar saldo inicial se não existir
    await executeQuery(
      'INSERT INTO treasurer_balances (user_id, current_balance, branch_id) VALUES (?, 0.00, ?)',
      [userId, req.user.branch_id]
    );

    return res.status(200).json({
      status: 'success',
      message: 'Saldo do tesoureiro obtido com sucesso',
      data: {
        balance: {
          current_balance: 0.00,
          branch_id: req.user.branch_id,
          branch_name: null,
          branch_code: null,
          created_at: new Date(),
          updated_at: new Date()
        }
      }
    });
  }

  res.status(200).json({
    status: 'success',
    message: 'Saldo do tesoureiro obtido com sucesso',
    data: {
      balance: balances[0]
    }
  });
}));

/**
 * GET /api/treasury/my-movements
 * Obter histórico de movimentações do tesoureiro logado
 */
router.get('/my-movements', authorize('tesoureiro'), catchAsync(async (req, res, next) => {
  const userId = req.user.id;
  const { page = 1, limit = 20, movement_type, start_date, end_date } = req.query;

  const offset = (parseInt(page) - 1) * parseInt(limit);

  // Construir query com filtros
  let whereConditions = ['tm.treasurer_id = ?'];
  let queryParams = [userId];

  if (movement_type) {
    whereConditions.push('tm.movement_type = ?');
    queryParams.push(movement_type);
  }

  if (start_date) {
    whereConditions.push('DATE(tm.created_at) >= ?');
    queryParams.push(start_date);
  }

  if (end_date) {
    whereConditions.push('DATE(tm.created_at) <= ?');
    queryParams.push(end_date);
  }

  const whereClause = whereConditions.join(' AND ');

  // Buscar movimentações
  const movements = await executeQuery(
    `SELECT tm.id, tm.movement_type, tm.amount, tm.balance_before, tm.balance_after,
            tm.source_type, tm.source_id, tm.reference_id, tm.description, tm.created_at,
            u.full_name as created_by_name
     FROM treasurer_movements tm
     LEFT JOIN users u ON tm.created_by = u.id
     WHERE ${whereClause}
     ORDER BY tm.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, parseInt(limit), offset]
  );

  // Contar total
  const totalResult = await executeQuery(
    `SELECT COUNT(*) as total FROM treasurer_movements tm WHERE ${whereClause}`,
    queryParams
  );

  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / parseInt(limit));

  res.status(200).json({
    status: 'success',
    message: 'Movimentações obtidas com sucesso',
    data: {
      movements,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages
      }
    }
  });
}));

/**
 * GET /api/treasury/pending-deliveries
 * Listar entregas pendentes de confirmação para o tesoureiro
 */
router.get('/pending-deliveries', authorize('tesoureiro'), catchAsync(async (req, res, next) => {
  const { page = 1, limit = 20 } = req.query;
  const offset = (parseInt(page) - 1) * parseInt(limit);

  // Obter entregas pendentes dirigidas à tesouraria
  const deliveries = await executeQuery(
    `SELECT
      td.id,
      td.amount,
      td.denominations,
      td.notes,
      td.delivery_type,
      td.created_at,
      td.cash_register_id,
      td.vault_id,
      u.full_name as delivered_by_name,
      u.email as delivered_by_email,
      r.name as delivered_by_role,
      cr.register_number,
      cr.description as cash_register_description,
      b.name as branch_name,
      b.code as branch_code
    FROM treasury_deliveries td
    JOIN users u ON td.delivered_by = u.id
    JOIN roles r ON u.role_id = r.id
    LEFT JOIN cash_registers cr ON td.cash_register_id = cr.id
    LEFT JOIN branches b ON cr.branch_id = b.id
    WHERE td.status = 'pending'
    ORDER BY td.created_at ASC
    LIMIT ? OFFSET ?`,
    [parseInt(limit), offset]
  );

  // Contar total de entregas pendentes
  const totalResult = await executeQuery(
    'SELECT COUNT(*) as total FROM treasury_deliveries WHERE status = ?',
    ['pending']
  );

  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / parseInt(limit));

  // Formatar dados para o frontend
  const formattedDeliveries = deliveries.map(delivery => {
    let sourceName = 'Origem Desconhecida';

    if (delivery.cash_register_id) {
      sourceName = `Caixa ${delivery.register_number}`;
      if (delivery.cash_register_description) {
        sourceName += ` (${delivery.cash_register_description})`;
      }
    } else if (delivery.vault_id) {
      sourceName = 'Cofre Principal';
    }

    return {
      id: delivery.id,
      amount: parseFloat(delivery.amount),
      denominations: JSON.parse(delivery.denominations),
      notes: delivery.notes,
      delivery_type: delivery.delivery_type,
      created_at: delivery.created_at,
      source_name: sourceName,
      delivered_by: {
        name: delivery.delivered_by_name,
        email: delivery.delivered_by_email,
        role: delivery.delivered_by_role
      },
      branch: delivery.branch_name ? {
        name: delivery.branch_name,
        code: delivery.branch_code
      } : null
    };
  });

  res.status(200).json({
    status: 'success',
    message: 'Entregas pendentes obtidas com sucesso',
    data: {
      deliveries: formattedDeliveries,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: parseInt(page) < totalPages,
        hasPrev: parseInt(page) > 1
      }
    }
  });
}));

/**
 * POST /api/treasury/confirm-delivery/:id
 * Confirmar entrega e atualizar saldo do tesoureiro
 */
router.post('/confirm-delivery/:id', authorize('tesoureiro'), catchAsync(async (req, res, next) => {
  const deliveryId = req.params.id;
  const { confirmed_amount, observations } = req.body;
  const treasurerId = req.user.id;

  // Validar dados de entrada
  if (!confirmed_amount || confirmed_amount <= 0) {
    return next(new AppError('Valor confirmado deve ser maior que zero', 400, 'VALIDATION_ERROR'));
  }

  // Verificar se a entrega existe e está pendente
  const deliveries = await executeQuery(
    `SELECT td.*, u.full_name as delivered_by_name, u.email as delivered_by_email,
            cr.register_number, cr.description as cash_register_description,
            b.name as branch_name
     FROM treasury_deliveries td
     JOIN users u ON td.delivered_by = u.id
     LEFT JOIN cash_registers cr ON td.cash_register_id = cr.id
     LEFT JOIN branches b ON cr.branch_id = b.id
     WHERE td.id = ? AND td.status = 'pending'`,
    [deliveryId]
  );

  if (!deliveries.length) {
    return next(new AppError('Entrega não encontrada ou já foi processada', 404, 'DELIVERY_NOT_FOUND'));
  }

  const delivery = deliveries[0];
  const declaredAmount = parseFloat(delivery.amount);
  const confirmedAmount = parseFloat(confirmed_amount);
  const differenceAmount = confirmedAmount - declaredAmount;

  try {
    // Iniciar transação
    await executeQuery('START TRANSACTION');

    // 1. Atualizar status da entrega
    const confirmationStatus = differenceAmount !== 0 ? 'confirmed_with_difference' : 'confirmed';

    await executeQuery(
      `UPDATE treasury_deliveries
       SET status = ?, confirmed_by = ?, confirmed_at = NOW(),
           notes = CONCAT(COALESCE(notes, ''), ?, ?)
       WHERE id = ?`,
      [
        confirmationStatus,
        treasurerId,
        observations ? `\n\nObservações da confirmação: ${observations}` : '',
        differenceAmount !== 0 ? `\n\nDiferença detectada: ${differenceAmount > 0 ? '+' : ''}${differenceAmount.toFixed(2)} AOA` : '',
        deliveryId
      ]
    );

    // 2. Obter saldo atual do tesoureiro
    const treasurerBalances = await executeQuery(
      'SELECT current_balance FROM treasurer_balances WHERE treasurer_id = ?',
      [treasurerId]
    );

    let currentBalance = 0;
    if (treasurerBalances.length > 0) {
      currentBalance = parseFloat(treasurerBalances[0].current_balance);
    } else {
      // Criar registo de saldo se não existir
      await executeQuery(
        'INSERT INTO treasurer_balances (treasurer_id, current_balance) VALUES (?, 0)',
        [treasurerId]
      );
    }

    const newBalance = currentBalance + confirmedAmount;

    // 3. Atualizar saldo do tesoureiro
    await executeQuery(
      'UPDATE treasurer_balances SET current_balance = ?, updated_at = NOW() WHERE treasurer_id = ?',
      [newBalance, treasurerId]
    );

    // 4. Criar movimento no histórico do tesoureiro
    await executeQuery(
      `INSERT INTO treasurer_movements
       (treasurer_id, movement_type, amount, balance_before, balance_after,
        source_type, source_id, reference_id, description, created_by)
       VALUES (?, 'credit', ?, ?, ?, 'treasury_delivery', ?, ?, ?, ?)`,
      [
        treasurerId,
        confirmedAmount,
        currentBalance,
        newBalance,
        deliveryId,
        delivery.cash_register_id || null,
        `Confirmação de entrega ${delivery.cash_register_id ? `do Caixa ${delivery.register_number}` : 'do Cofre Principal'}`,
        treasurerId
      ]
    );

    // 5. Se há diferença, criar alerta
    if (differenceAmount !== 0) {
      const alertId = uuidv4();

      await executeQuery(
        `INSERT INTO cash_difference_alerts
         (id, delivery_id, cash_register_id, operator_id, treasurer_id,
          declared_amount, confirmed_amount, difference_amount, status, created_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())`,
        [
          alertId,
          deliveryId,
          delivery.cash_register_id,
          delivery.delivered_by,
          treasurerId,
          declaredAmount,
          confirmedAmount,
          differenceAmount
        ]
      );

      // Criar notificação de alerta para Gerente/Admin
      try {
        const notificationService = require('../notifications/notificationService');
        await notificationService.createCashDifferenceAlert({
          id: alertId,
          delivery_id: deliveryId,
          difference_amount: differenceAmount,
          operator_name: delivery.delivered_by_name,
          source_name: delivery.cash_register_id ? `Caixa ${delivery.register_number}` : 'Cofre Principal'
        });
      } catch (notifError) {
        logger.error('Erro ao criar notificação de alerta:', notifError);
        // Não falhar a transação por causa da notificação
      }
    }

    // 6. Criar notificação de confirmação para o operador
    try {
      const notificationService = require('../notifications/notificationService');
      await notificationService.createTreasuryDeliveryConfirmedNotification(
        {
          id: deliveryId,
          amount: confirmedAmount,
          delivered_by: delivery.delivered_by
        },
        req.user.full_name
      );
    } catch (notifError) {
      logger.error('Erro ao criar notificação de confirmação:', notifError);
      // Não falhar a transação por causa da notificação
    }

    // Confirmar transação
    await executeQuery('COMMIT');

    logger.info(`Entrega ${deliveryId} confirmada pelo tesoureiro ${req.user.email}`, {
      deliveryId,
      treasurerId,
      declaredAmount,
      confirmedAmount,
      differenceAmount,
      hasAlert: differenceAmount !== 0
    });

    res.status(200).json({
      status: 'success',
      message: 'Entrega confirmada com sucesso',
      data: {
        delivery_id: deliveryId,
        declared_amount: declaredAmount,
        confirmed_amount: confirmedAmount,
        difference_amount: differenceAmount,
        new_balance: newBalance,
        has_difference: differenceAmount !== 0,
        confirmation_status: confirmationStatus
      }
    });

  } catch (error) {
    // Reverter transação em caso de erro
    await executeQuery('ROLLBACK');

    logger.error('Erro ao confirmar entrega:', {
      error: error.message,
      deliveryId,
      treasurerId
    });

    throw error;
  }
}));

module.exports = router;
