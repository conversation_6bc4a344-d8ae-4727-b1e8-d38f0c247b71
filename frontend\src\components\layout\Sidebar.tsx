
import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { menuItems } from '@/config/menuItems';
import { useAuth } from '@/contexts/AuthContext';
import { useActiveSessions } from '@/hooks/useActiveSessions';
import { cashRegisterSessionService } from '@/services/cashRegisterSessionService';

// Interface para as propriedades do componente Sidebar
interface SidebarProps {
  isCollapsed: boolean; // Estado de colapso da barra lateral
  onToggle: () => void; // Função para alternar o estado de colapso
}

// Função auxiliar para obter classes CSS dos ícones colapsados
const getCollapsedIconClasses = (isActive: boolean, isDisabled: boolean) => {
  return cn(
    // Container do ícone: flex centralizado, tamanho fixo, bordas arredondadas, transições
    "flex items-center justify-center h-8 w-8 rounded-lg text-sm transition-all duration-200 shadow-sm",
    isActive
      // Estado ativo: fundo branco, texto lilac para melhor contraste, sombra e escala
      ? "bg-white dark:bg-gray-800 text-twins-primary shadow-lg scale-105 border border-twins-primary"
      : isDisabled
      // Estado desabilitado: texto acinzentado, cursor não permitido
      ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
      // Estado normal: texto padrão, hover com fundo lilac claro e texto lilac
      : "text-gray-600 dark:text-gray-300 hover:bg-twins-accent dark:hover:bg-twins-accent/20 hover:scale-105 hover:shadow-md hover:text-twins-primary dark:hover:text-twins-primary"
  );
};

// Componente auxiliar para renderizar ícones colapsados com tooltip
const CollapsedMenuItem = ({ item, to, isActive, title }: {
  item: any;
  to: string;
  isActive: boolean;
  title: string;
}) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <NavLink
        to={to}
        end
        className={({ isActive: routerIsActive }) => {
          // Usar o isActive passado como prop ou o do router, priorizando o prop
          const shouldBeActive = isActive || routerIsActive;
          return getCollapsedIconClasses(shouldBeActive, item.disabled);
        }}
      >
        <item.icon className="h-5 w-5" />
      </NavLink>
    </TooltipTrigger>
    <TooltipContent side="right" className="dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600">
      <p>{title}</p>
    </TooltipContent>
  </Tooltip>
);

// Componente principal da barra lateral de navegação
const Sidebar = ({ isCollapsed, onToggle }: SidebarProps) => {
  // Estado para controlar qual submenu está aberto (padrão: tesouraria)
  const [openSubmenu, setOpenSubmenu] = useState<string | null>('tesouraria');

  // Estado para verificar se há sessão de caixa ativa
  const [hasActiveSession, setHasActiveSession] = useState<boolean>(false);

  // Hook para obter a localização atual da rota
  const location = useLocation();

  // Hook para verificar permissões e roles do usuário autenticado
  const { user, hasPermission, hasRole } = useAuth();

  // Hook para carregar sessões ativas de caixa
  const { activeSessionsCount } = useActiveSessions();

  // Verificar estado da sessão de caixa ao carregar o componente
  useEffect(() => {
    const checkCashSession = async () => {
      try {
        const session = await cashRegisterSessionService.getCurrentSession();
        setHasActiveSession(!!session);
      } catch (error) {
        setHasActiveSession(false);
      }
    };

    // Só verificar para usuários com perfil de caixa
    if (user?.role === 'caixa') {
      checkCashSession();
    }
  }, [user?.role]);

  // Função para alternar a abertura/fechamento de submenus
  // Só funciona quando a sidebar não está colapsada
  const toggleSubmenu = (itemId: string) => {
    if (!isCollapsed) {
      setOpenSubmenu(openSubmenu === itemId ? null : itemId);
    }
  };

  // Função para verificar se o usuário tem acesso a um item específico do menu
  // Implementa RBAC com diferentes níveis de acesso
  const hasAccessToMenuItem = (itemId: string): boolean => {
    switch (itemId) {
      case 'dashboard':
        return true; // Dashboard é acessível para todos os usuários autenticados
      case 'clientes':
        return hasPermission('clientes', 'read'); // Requer permissão de leitura em clientes
      case 'contas':
        return hasPermission('accounts', 'read'); // Requer permissão de leitura em contas
      case 'caixa':
        // Tesoureiro: OCULTO (não tem acesso ao menu Caixa)
        if (user?.role === 'tesoureiro') return false;
        return hasPermission('caixa', 'read'); // Requer permissão de leitura em caixa
      case 'tesouraria':
        return hasPermission('tesouraria', 'read'); // Requer permissão de leitura em tesouraria
      case 'transferencias':
        return hasPermission('transferencias', 'read'); // Requer permissão de leitura em transferências
      case 'cartoes':
        return hasPermission('cartoes', 'read'); // Requer permissão de leitura em cartões
      case 'cambios':
        return hasPermission('cambios', 'read'); // Requer permissão de leitura em câmbios
      case 'seguros':
        return hasPermission('seguros', 'read'); // Requer permissão de leitura em seguros
      case 'sistema':
        return hasRole(['admin', 'gerente', 'tecnico']); // Sistema para admin, gerente e técnico
      case 'atm':
        return hasPermission('atm', 'read'); // Requer permissão de leitura em ATM
      default:
        return false; // Por padrão, negar acesso a itens não reconhecidos
    }
  };

  // Função para verificar se um item deve ser desabilitado (visível mas inativo)
  // Para perfis de supervisão que devem ver mas não executar certas ações
  const isMenuItemDisabled = (itemId: string, path?: string): boolean => {
    const userRole = user?.role;

    // Regras específicas para desabilitação por supervisão
    if (userRole === 'admin' || userRole === 'gerente') {
      // Operações operacionais que supervisores podem ver mas não executar
      if (path === '/caixa/abertura-caixa' || path === '/caixa') {
        return true; // Visível mas desabilitado para supervisores
      }
      // Operações de tesouraria exclusivas para tesoureiro
      if (path === '/tesouraria/entrega-caixa' || path === '/tesouraria/entrega-cofre') {
        return true; // Visível mas desabilitado para admin/gerente
      }
      // Carregamento de ATM exclusivo para tesoureiro
      if (path === '/tesouraria/carregamento-atm') {
        return true; // Visível mas desabilitado para admin/gerente
      }
      // Entrega ao Balcão exclusiva para tesoureiro
      if (path === '/sistema/entrega-balcao') {
        return true; // Visível mas desabilitado para admin/gerente
      }
    }

    return false; // Por padrão, não desabilitar
  };

  // Função para obter tooltip explicativo para itens desabilitados
  const getDisabledTooltip = (itemId: string, path?: string): string => {
    const userRole = user?.role;

    if (userRole === 'admin' || userRole === 'gerente') {
      if (path === '/caixa/abertura-caixa' || path === '/caixa') {
        return "Ação exclusiva para o perfil 'Operador de Caixa'";
      }
      if (path === '/tesouraria/entrega-caixa' || path === '/tesouraria/entrega-cofre') {
        return "Ação exclusiva para o perfil 'Tesoureiro'";
      }
      if (path === '/tesouraria/carregamento-atm') {
        return "Ação exclusiva para o perfil 'Tesoureiro'";
      }
      if (path === '/sistema/entrega-balcao') {
        return "Ação exclusiva para o perfil 'Tesoureiro'";
      }
    }

    return 'Acesso restrito para o seu perfil';
  };

  // Função para verificar se o usuário tem acesso a um submenu específico
  // Implementa regras específicas de RBAC incluindo perfil técnico
  const hasAccessToSubmenu = (path: string): boolean => {
    const userRole = user?.role;

    switch (path) {
      // Submenus de Caixa - Regras específicas por perfil
      case '/caixa/abertura-caixa':
        // Técnico: OCULTO (não tem acesso a dados financeiros)
        if (userRole === 'tecnico') return false;
        // Tesoureiro: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'tesoureiro') return false;
        // Balcão: OCULTO (não tem acesso a operações de caixa)
        if (userRole === 'balcao') return false;
        // Admin/Gerente: VISÍVEL (para supervisão)
        // Caixa: ATIVO (operacional)
        return hasRole(['admin', 'gerente', 'caixa']);

      case '/caixa':
        // Técnico: OCULTO (não tem acesso a dados financeiros)
        if (userRole === 'tecnico') return false;
        // Tesoureiro: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'tesoureiro') return false;
        // Balcão: OCULTO (não tem acesso a operações de caixa)
        if (userRole === 'balcao') return false;
        // Admin/Gerente: VISÍVEL (para supervisão)
        // Caixa: ATIVO (operacional)
        return hasRole(['admin', 'gerente', 'caixa']);

      case '/caixa/diferencas-caixa':
        // Visível APENAS para Admin e Gerente (gestão de alertas)
        return hasRole(['admin', 'gerente']);

      case '/sistema/caixas-abertos':
        // Técnico: OCULTO (não tem acesso a dados financeiros)
        if (userRole === 'tecnico') return false;
        // Tesoureiro: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'tesoureiro') return false;
        // Balcão: OCULTO (não tem acesso a gestão de caixas)
        if (userRole === 'balcao') return false;
        // Admin/Gerente: VISÍVEL (para supervisão)
        // Caixa: OCULTO (não deve ver este submenu)
        return hasRole(['admin', 'gerente']);

      case '/sistema/caixas':
        // Técnico: OCULTO (não gerencia operações financeiras)
        if (userRole === 'tecnico') return false;
        // Balcão: OCULTO (não tem acesso a gestão de caixas)
        if (userRole === 'balcao') return false;
        return hasRole(['admin', 'gerente']);

      // Submenus de Tesouraria - Regras específicas por perfil
      case '/tesouraria/meu-caixa':
        // Visível APENAS para tesoureiro
        return hasRole(['tesoureiro']);

      case '/tesouraria/entrega-caixa':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      case '/tesouraria/gestao-cofre':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        // Tesoureiro: OCULTO (conforme requisitos do sistema)
        if (userRole === 'tesoureiro') return false;
        return hasRole(['admin', 'gerente']);

      case '/tesouraria/entrega-cofre':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      case '/sistema/entrega-tesoureiro':
        // Técnico: OCULTO (não gerencia operações financeiras)
        if (userRole === 'tecnico') return false;
        // Balcão: VISÍVEL (pode entregar dinheiro ao tesoureiro)
        return hasRole(['admin', 'gerente', 'balcao']);

      case '/sistema/entrega-balcao':
        // Técnico: OCULTO (não gerencia operações financeiras)
        if (userRole === 'tecnico') return false;
        // Balcão: OCULTO (não pode entregar para si mesmo)
        if (userRole === 'balcao') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        // Admin/Gerente: VISÍVEL (para supervisão)
        // Tesoureiro: ATIVO (operacional)
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      case '/tesouraria/carregamento-atm':
        // Técnico: OCULTO (não tem acesso a operações financeiras)
        if (userRole === 'tecnico') return false;
        // Caixa: OCULTO (irrelevante para seu fluxo)
        if (userRole === 'caixa') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro']);

      // Submenus de Sistema - Regras específicas para técnico
      case '/sistema/auditoria':
        // Técnico: VISÍVEL (pode acessar logs técnicos)
        return hasRole(['admin', 'gerente', 'tecnico']);

      case '/sistema/registar-usuario':
      case '/sistema/listar-usuario':
      case '/sistema/registar-role':
      case '/sistema/definir-tarefas':
      case '/sistema/data-sistema':
        // Técnico: OCULTO (não gerencia configurações de negócio)
        if (userRole === 'tecnico') return false;
        return hasRole(['admin', 'gerente']);

      case '/sistema/registar-balcao':
        // Técnico: VISÍVEL (pode precisar para configurações técnicas)
        return hasRole(['admin', 'gerente', 'tecnico']);

      // Submenus de Clientes, Contas, Transferências, Cartões, Câmbios, Seguros
      case '/clientes/gestao-clientes':
      case '/clientes/abrir-conta-particular':
      case '/clientes/abrir-conta-empresa':
      case '/contas/gestao-contas':
      case '/clientes/aprovacao-contas':
      case '/transferencias':
      case '/cartoes':
      case '/cambios':
      case '/seguros':
        // Técnico: OCULTO (não tem acesso a dados financeiros/operacionais)
        if (userRole === 'tecnico') return false;
        return true; // Outros perfis seguem regras padrão

      // ATM - Técnico tem acesso para monitoramento técnico
      case '/atm':
        // Balcão: OCULTO (não tem acesso a gestão de ATMs)
        if (userRole === 'balcao') return false;
        return hasRole(['admin', 'gerente', 'tesoureiro', 'tecnico']);

      // Por padrão, permitir acesso se o usuário tem acesso ao menu principal
      default:
        return true;
    }
  };

  // Função para criar menu dinâmico baseado no estado da sessão
  const getDynamicMenuItems = () => {
    return menuItems.map(item => {
      // Modificar o submenu de Caixa para usuários com perfil de caixa
      if (item.id === 'caixa' && user?.role === 'caixa' && item.submenu) {
        return {
          ...item,
          submenu: item.submenu.map(subItem => {
            // Alterar "Abertura do Caixa" para "Fechar Caixa" quando há sessão ativa
            if (subItem.path === '/caixa/abertura-caixa' && hasActiveSession) {
              return {
                ...subItem,
                title: 'Fechar Caixa',
                path: '/caixa/fechamento-caixa'
              };
            }
            return subItem;
          })
        };
      }
      return item;
    });
  };

  // Filtrar itens de menu baseado nas permissões do usuário autenticado
  // Só mostra os itens que o usuário tem permissão para acessar
  const filteredMenuItems = getDynamicMenuItems().filter(item => hasAccessToMenuItem(item.id)).map(item => {
    // Se o item tem submenu, filtrar os submenus baseado nas permissões
    if (item.submenu) {
      return {
        ...item,
        submenu: item.submenu.filter(subItem => hasAccessToSubmenu(subItem.path))
      };
    }
    return item;
  });



  return (
    <TooltipProvider>
      {/* Container principal da barra lateral */}
      <div className={cn(
        // Estilos base: fundo, borda, altura total, transições e layout flex
        "bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 h-screen transition-all duration-300 flex flex-col overflow-hidden",
        "hidden md:flex", // Oculta no mobile, mostra no desktop
        // Largura dinâmica baseada no estado de colapso
        isCollapsed ? "w-16" : "w-64"
      )}>
        {/* Cabeçalho com logo - Fixo no topo */}
        <div className={cn(
          // Padding, borda inferior, layout flex e posicionamento
          "p-4 h-16 border-b border-gray-200 dark:border-gray-700 flex items-center relative flex-shrink-0",
          // Centraliza o logo quando colapsado
          isCollapsed ? "justify-center" : ""
        )}>
          {/* Renderização condicional do logo baseada no estado de colapso */}
          {!isCollapsed ? (
            // Logo completo quando expandido
            <h1 className="text-xl font-bold bg-gradient-to-r from-twins-primary to-twins-secondary bg-clip-text text-transparent">twins_bank</h1>
          ) : (
            // Logo abreviado quando colapsado
            <div className="text-xl font-bold bg-gradient-to-r from-twins-primary to-twins-secondary bg-clip-text text-transparent">TB</div>
          )}
        </div>

      {/* Área de navegação - Rolável */}
      <nav className={cn(
        // Estilos base: flex-1 para ocupar espaço restante, overflow para scroll, scrollbar customizada
        "flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-800",
        // Layout condicional baseado no estado de colapso
        isCollapsed
          ? "flex flex-col items-center py-3 space-y-3" // Colapsado: flex column, ícones centralizados, padding vertical, espaçamento entre itens
          : "p-2 space-y-2" // Expandido: padding padrão, espaçamento menor
      )}>
        {/* Mapear e renderizar todos os itens de menu filtrados */}
        {filteredMenuItems.map((item) => {
          // Verificar se o item tem submenu
          if (item.submenu) {
            // Renderização especial para itens com submenu quando a sidebar está colapsada
            if (isCollapsed) {
              // Verificar se o submenu tem itens após filtragem
              if (item.submenu.length === 0) {
                return null; // Não renderizar se não há itens no submenu
              }

              // Verificar se algum item do submenu está ativo
              const isAnySubmenuActive = item.submenu.some((subItem: any) => location.pathname === subItem.path);
              const firstSubItem = item.submenu[0];

              // Aplicar roteamento condicional para "Entrega ao Tesoureiro"
              let targetPath = firstSubItem.path;
              if (firstSubItem.path === '/sistema/entrega-tesoureiro' && (user?.role === 'admin' || user?.role === 'gerente')) {
                targetPath = '/tesouraria/entrega-tesoureiro';
              }

              return (
                <CollapsedMenuItem
                  key={item.id}
                  item={item}
                  to={targetPath}
                  isActive={isAnySubmenuActive}
                  title={item.title}
                />
              );
            }

            // Renderização para submenu quando a sidebar está expandida
            return (
              // Componente colapsível para controlar abertura/fechamento do submenu
              <Collapsible
                key={item.id}
                open={!isCollapsed && openSubmenu === item.id} // Só abre se não estiver colapsado e for o submenu selecionado
                onOpenChange={() => toggleSubmenu(item.id)} // Função para alternar estado
              >
                {/* Botão trigger para abrir/fechar o submenu */}
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      // Estilos base: largura total, alinhamento à esquerda, altura, bordas, transições
                      "w-full justify-start h-10 rounded-lg transition-all duration-200 dark:text-gray-300 dark:hover:text-twins-primary",
                      // Padding condicional baseado no estado de colapso
                      isCollapsed ? "px-2" : "px-3",
                      // Efeitos de hover: fundo lilac claro, escala e sombra
                      "hover:bg-twins-accent dark:hover:bg-twins-accent/20 hover:scale-105 hover:shadow-md"
                    )}
                    disabled={item.disabled}
                  >
                    {/* Ícone do item principal */}
                    <item.icon className={cn("h-5 w-5", isCollapsed ? "" : "mr-3")} />
                    {/* Conteúdo adicional só quando expandido */}
                    {!isCollapsed && (
                      <>
                        {/* Título do item */}
                        <span className="flex-1 text-left">{item.title}</span>
                        {/* Ícone indicador de estado (aberto/fechado) */}
                        {openSubmenu === item.id ? (
                          <ChevronDown className="h-4 w-4" /> // Seta para baixo quando aberto
                        ) : (
                          <ChevronRight className="h-4 w-4" /> // Seta para direita quando fechado
                        )}
                      </>
                    )}
                  </Button>
                </CollapsibleTrigger>
                {/* Conteúdo do submenu - só renderiza quando não está colapsado */}
                {!isCollapsed && (
                  <CollapsibleContent className="ml-6 space-y-1"> {/* Margem esquerda para indentação, espaçamento entre itens */}
                    {/* Mapear todos os itens do submenu */}
                    {item.submenu.map((subItem) => {
                      // Verificar se o item deve ser desabilitado
                      const isDisabled = isMenuItemDisabled(item.id, subItem.path);
                      const tooltipText = isDisabled ? getDisabledTooltip(item.id, subItem.path) : '';
                      const isExactMatch = location.pathname === subItem.path;

                      const menuItemContent = isDisabled ? (
                        // Elemento desabilitado (div) - não usa NavLink para evitar conflitos de roteamento
                        <div
                          key={subItem.path}
                          className={cn(
                            // Estilos base: flex, altura, padding, bordas, transições
                            "flex items-center h-9 px-3 rounded-lg text-sm transition-all duration-200 shadow-sm",
                            // Estado desabilitado: texto acinzentado, cursor não permitido
                            "text-gray-400 dark:text-gray-600 cursor-not-allowed opacity-60"
                          )}
                        >
                          {/* Ícone do subitem */}
                          <subItem.icon className="h-4 w-4 mr-3" />
                          {/* Título do subitem */}
                          <span className="flex-1">{subItem.title}</span>
                          {/* Badge para Caixas Abertos */}
                          {subItem.path === '/sistema/caixas-abertos' && activeSessionsCount > 0 && (
                            <span className="ml-2 bg-gray-400 text-white text-xs font-medium px-2 py-0.5 rounded-full min-w-[20px] text-center opacity-60">
                              {activeSessionsCount}
                            </span>
                          )}
                        </div>
                      ) : (
                        // Elemento ativo (NavLink) - funcionalidade normal de navegação
                        <NavLink
                          key={subItem.path}
                          to={
                            // Roteamento condicional para "Entrega ao Tesoureiro"
                            subItem.path === '/sistema/entrega-tesoureiro' && (user?.role === 'admin' || user?.role === 'gerente')
                              ? '/tesouraria/entrega-tesoureiro'
                              : subItem.path
                          }
                          end
                          className={({ isActive }) => {
                            return cn(
                              // Estilos base: flex, altura, padding, bordas, transições
                              "flex items-center h-9 px-3 rounded-lg text-sm transition-all duration-200 shadow-sm",
                              isActive
                                // Estado ativo: fundo lilac, texto branco para contraste, sombra e escala
                                ? "bg-gradient-to-r from-twins-primary to-twins-secondary text-white shadow-md scale-105"
                                // Estado normal: texto padrão, hover com fundo lilac claro
                                : "text-gray-700 dark:text-gray-300 hover:bg-twins-accent/50 dark:hover:bg-twins-accent/20 hover:scale-105 hover:shadow-md hover:text-twins-primary dark:hover:text-twins-primary"
                            );
                          }}
                        >
                          {/* Ícone do subitem */}
                          <subItem.icon className="h-4 w-4 mr-3" />
                          {/* Título do subitem */}
                          <span className="flex-1">{subItem.title}</span>
                          {/* Badge para Caixas Abertos */}
                          {subItem.path === '/sistema/caixas-abertos' && activeSessionsCount > 0 && (
                            <span className="ml-2 bg-green-500 text-white text-xs font-medium px-2 py-0.5 rounded-full min-w-[20px] text-center">
                              {activeSessionsCount}
                            </span>
                          )}
                        </NavLink>
                      );

                      // Se o item está desabilitado, envolver com tooltip
                      if (isDisabled) {
                        return (
                          <Tooltip key={subItem.path}>
                            <TooltipTrigger asChild>
                              {menuItemContent}
                            </TooltipTrigger>
                            <TooltipContent side="right" className="max-w-xs">
                              <p>{tooltipText}</p>
                            </TooltipContent>
                          </Tooltip>
                        );
                      }

                      return menuItemContent;
                    })}
                  </CollapsibleContent>
                )}
              </Collapsible>
            );
          }

          // Renderização para itens regulares (sem submenu) quando colapsado
          if (isCollapsed) {
            return (
              <CollapsedMenuItem
                key={item.id}
                item={item}
                to={item.path || '#'}
                isActive={location.pathname === item.path}
                title={item.title}
              />
            );
          }

          // Renderização para itens regulares (sem submenu) quando expandido
          return (
            <NavLink
              key={item.id}
              to={item.path || '#'}
              end
              className={({ isActive }) =>
                cn(
                  // Estilos base: flex, altura, bordas, transições, padding horizontal
                  "flex items-center h-10 rounded-lg text-sm transition-all duration-200 px-3 shadow-sm",
                  isActive
                    // Estado ativo: fundo lilac, texto branco para contraste, sombra e escala
                    ? "bg-gradient-to-r from-twins-primary to-twins-secondary text-white shadow-lg scale-105"
                    : item.disabled
                    // Estado desabilitado: texto acinzentado, cursor não permitido
                    ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
                    // Estado normal: texto padrão, hover com fundo lilac claro e texto lilac
                    : "text-gray-700 dark:text-gray-300 hover:bg-twins-accent dark:hover:bg-twins-accent/20 hover:scale-105 hover:shadow-md hover:text-twins-primary dark:hover:text-twins-primary"
                )
              }
            >
              {/* Ícone do item */}
              <item.icon className="h-5 w-5 mr-3" />
              {/* Título do item */}
              <span>{item.title}</span>
            </NavLink>
          );
        })}
      </nav>
      </div>
    </TooltipProvider>
  );
};

// Exportar o componente como padrão
export default Sidebar;
