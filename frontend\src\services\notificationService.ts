import { makeRequest } from '@/utils/api';

// Interfaces para notificações
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read: boolean;
  data?: any;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

class NotificationService {
  // Obter notificações do usuário
  async getNotifications(options: {
    page?: number;
    limit?: number;
    unread_only?: boolean;
    type_code?: string;
  } = {}): Promise<NotificationResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(options).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const response = await makeRequest<NotificationResponse>(
      `/notifications?${queryParams.toString()}`
    );

    if (response.data) {
      // Converter timestamps para Date objects
      const notifications = response.data.notifications.map(notification => ({
        ...notification,
        timestamp: new Date(notification.timestamp),
        read: notification.read || false
      }));

      return {
        ...response.data,
        notifications
      };
    }
    throw new Error(response.message || 'Erro ao carregar notificações');
  }

  // Marcar notificação como lida
  async markAsRead(notificationId: string): Promise<void> {
    const response = await makeRequest(`/notifications/${notificationId}/read`, {
      method: 'PUT'
    });

    if (!response.success) {
      throw new Error(response.message || 'Erro ao marcar notificação como lida');
    }
  }

  // Marcar todas as notificações como lidas
  async markAllAsRead(): Promise<void> {
    const response = await makeRequest('/notifications/mark-all-read', {
      method: 'PUT'
    });

    if (!response.success) {
      throw new Error(response.message || 'Erro ao marcar todas as notificações como lidas');
    }
  }

  // Remover notificação
  async removeNotification(notificationId: string): Promise<void> {
    const response = await makeRequest(`/notifications/${notificationId}`, {
      method: 'DELETE'
    });

    if (!response.success) {
      throw new Error(response.message || 'Erro ao remover notificação');
    }
  }

  // Limpar todas as notificações
  async clearAll(): Promise<void> {
    const response = await makeRequest('/notifications/clear-all', {
      method: 'DELETE'
    });

    if (!response.success) {
      throw new Error(response.message || 'Erro ao limpar notificações');
    }
  }

  // Obter contagem de notificações não lidas
  async getUnreadCount(): Promise<number> {
    const response = await makeRequest<{ count: number }>('/notifications/unread-count');

    if (response.data) {
      return response.data.count;
    }
    throw new Error(response.message || 'Erro ao obter contagem de notificações');
  }

  // Mapear tipo de notificação do backend para frontend
  private mapNotificationType(backendType: string): 'info' | 'success' | 'warning' | 'error' {
    switch (backendType) {
      case 'TREASURY_DELIVERY_PENDING':
      case 'TREASURY_DELIVERY_CONFIRMED':
      case 'CASH_REGISTER_OPENED':
      case 'CASH_REGISTER_CLOSED':
        return 'info';
      case 'CASH_DIFFERENCE_ALERT':
        return 'warning';
      case 'SYSTEM_ERROR':
        return 'error';
      default:
        return 'info';
    }
  }
}

export const notificationService = new NotificationService();
